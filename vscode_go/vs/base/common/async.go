/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"errors"
	"sync"
	"time"
)

// ITask represents a task that can be executed
type ITask[T any] func() T

// ITaskAsync represents an async task that can be executed
type ITaskAsync[T any] func() <-chan T

// Task is a convenience type for void tasks
type Task = ITask[interface{}]

// AsyncTask is a convenience type for async void tasks
type AsyncTask = ITaskAsync[interface{}]

// ILimiter interface for controlling concurrent execution
type ILimiter[T any] interface {
	Size() int
	Queue(factory ITaskAsync[T]) <-chan T
	Clear()
	Dispose()
}

// CancellationError represents a cancellation error
type CancellationError struct{}

func (e CancellationError) Error() string {
	return "operation was cancelled"
}

// CancelablePromise represents a promise that can be cancelled
type CancelablePromise[T any] interface {
	// Result returns a channel that will receive the result
	Result() <-chan T
	// Cancelled returns a channel that will be closed when cancelled
	Cancelled() <-chan struct{}
	// Cancel cancels the promise
	Cancel()
}

// cancelablePromiseImpl implements CancelablePromise
type cancelablePromiseImpl[T any] struct {
	result    chan T
	cancelled chan struct{}
	cancel    func()
	mu        sync.Mutex
	done      bool
}

// CreateCancelablePromise creates a new cancelable promise
func CreateCancelablePromise[T any](callback func() <-chan T) CancelablePromise[T] {
	result := make(chan T, 1)
	cancelled := make(chan struct{})

	impl := &cancelablePromiseImpl[T]{
		result:    result,
		cancelled: cancelled,
	}

	var cancelOnce sync.Once
	impl.cancel = func() {
		cancelOnce.Do(func() {
			close(cancelled)
		})
	}

	go func() {
		defer func() {
			impl.mu.Lock()
			if !impl.done {
				impl.done = true
				close(result)
			}
			impl.mu.Unlock()
		}()

		source := callback()
		select {
		case value := <-source:
			impl.mu.Lock()
			if !impl.done {
				impl.done = true
				result <- value
			}
			impl.mu.Unlock()
		case <-cancelled:
			// Promise was cancelled
		}
	}()

	return impl
}

func (p *cancelablePromiseImpl[T]) Result() <-chan T {
	return p.result
}

func (p *cancelablePromiseImpl[T]) Cancelled() <-chan struct{} {
	return p.cancelled
}

func (p *cancelablePromiseImpl[T]) Cancel() {
	p.cancel()
}

// ThrottledWorkerOptions represents options for throttled worker
type ThrottledWorkerOptions struct {
	MaxWorkChunkSize int
	ThrottleDelay    time.Duration
	MaxBufferedWork  int
}

// Delayer schedules a function to run after a delay, with subsequent calls resetting the timer
type Delayer struct {
	runner     func()
	timeout    time.Duration
	timer      *time.Timer
	mu         sync.Mutex
	isDisposed bool
}

// NewDelayer creates a new Delayer
func NewDelayer(runner func(), delay time.Duration) *Delayer {
	return &Delayer{
		runner:  runner,
		timeout: delay,
	}
}

// Dispose disposes the scheduler
func (r *Delayer) Dispose() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.Cancel()
	r.runner = nil
	r.isDisposed = true
}

// Cancel cancels the current scheduled runner (if any)
func (r *Delayer) Cancel() {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.timer != nil {
		r.timer.Stop()
		r.timer = nil
	}
}

// Schedule cancels previous runner (if any) and schedules a new runner
func (r *Delayer) Schedule(delay ...time.Duration) {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.isDisposed {
		return
	}

	// Cancel existing timer
	if r.timer != nil {
		r.timer.Stop()
	}

	// Use provided delay or default timeout
	scheduleDelay := r.timeout
	if len(delay) > 0 {
		scheduleDelay = delay[0]
	}

	// Schedule new timer
	r.timer = time.AfterFunc(scheduleDelay, func() {
		r.mu.Lock()
		defer r.mu.Unlock()

		r.timer = nil
		if r.runner != nil && !r.isDisposed {
			r.doRun()
		}
	})
}

// Delay gets the current delay
func (r *Delayer) Delay() time.Duration {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.timeout
}

// SetDelay sets the delay
func (r *Delayer) SetDelay(delay time.Duration) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.timeout = delay
}

// IsScheduled returns true if scheduled
func (r *Delayer) IsScheduled() bool {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.timer != nil
}

// Flush executes the runner immediately if scheduled
func (r *Delayer) Flush() {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.timer != nil {
		r.timer.Stop()
		r.timer = nil
		if r.runner != nil && !r.isDisposed {
			r.doRun()
		}
	}
}

// doRun executes the runner function
func (r *Delayer) doRun() {
	if r.runner != nil {
		r.runner()
	}
}

// ThrottledWorker represents a throttled worker
type ThrottledWorker[T any] struct {
	options   ThrottledWorkerOptions
	processor func([]T)
	buffer    []T
	pending   int
	mutex     sync.Mutex
	timer     *time.Timer
}

// NewThrottledWorker creates a new throttled worker
func NewThrottledWorker[T any](options ThrottledWorkerOptions, processor func([]T)) *ThrottledWorker[T] {
	return &ThrottledWorker[T]{
		options:   options,
		processor: processor,
		buffer:    make([]T, 0),
	}
}

// Work adds work to the throttled worker
func (tw *ThrottledWorker[T]) Work(items []T) bool {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()

	// Check if we can accept more work
	if len(tw.buffer)+len(items) > tw.options.MaxBufferedWork {
		return false
	}

	tw.buffer = append(tw.buffer, items...)
	tw.pending = len(tw.buffer)

	// Schedule processing
	if tw.timer != nil {
		tw.timer.Stop()
	}

	tw.timer = time.AfterFunc(tw.options.ThrottleDelay, func() {
		tw.processBuffer()
	})

	return true
}

// Pending returns the number of pending items
func (tw *ThrottledWorker[T]) Pending() int {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()
	return tw.pending
}

// processBuffer processes the buffered items
func (tw *ThrottledWorker[T]) processBuffer() {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()

	if len(tw.buffer) == 0 {
		return
	}

	// Process in chunks
	chunkSize := tw.options.MaxWorkChunkSize
	if chunkSize <= 0 || chunkSize > len(tw.buffer) {
		chunkSize = len(tw.buffer)
	}

	chunk := make([]T, chunkSize)
	copy(chunk, tw.buffer[:chunkSize])
	tw.buffer = tw.buffer[chunkSize:]
	tw.pending = len(tw.buffer)

	// Process the chunk
	go tw.processor(chunk)

	// Schedule next processing if there's more work
	if len(tw.buffer) > 0 {
		tw.timer = time.AfterFunc(tw.options.ThrottleDelay, func() {
			tw.processBuffer()
		})
	}
}

// RunOnceWorker represents a run-once worker
type RunOnceWorker[T any] struct {
	processor func([]T)
	delay     time.Duration
	buffer    []T
	timer     *time.Timer
	mutex     sync.Mutex
}

// NewRunOnceWorker creates a new run-once worker
func NewRunOnceWorker[T any](processor func([]T), delay time.Duration) *RunOnceWorker[T] {
	return &RunOnceWorker[T]{
		processor: processor,
		delay:     delay,
		buffer:    make([]T, 0),
	}
}

// Work adds work to the run-once worker
func (row *RunOnceWorker[T]) Work(item T) {
	row.mutex.Lock()
	defer row.mutex.Unlock()

	row.buffer = append(row.buffer, item)

	// Reset timer
	if row.timer != nil {
		row.timer.Stop()
	}

	row.timer = time.AfterFunc(row.delay, func() {
		row.flush()
	})
}

// Flush processes all buffered items immediately
func (row *RunOnceWorker[T]) Flush() {
	row.mutex.Lock()
	defer row.mutex.Unlock()
	row.flush()
}

// flush processes all buffered items (internal, assumes mutex is held)
func (row *RunOnceWorker[T]) flush() {
	if len(row.buffer) == 0 {
		return
	}

	items := make([]T, len(row.buffer))
	copy(items, row.buffer)
	row.buffer = row.buffer[:0] // Clear buffer

	if row.timer != nil {
		row.timer.Stop()
		row.timer = nil
	}

	go row.processor(items)
}

// Future represents the result of an asynchronous operation.

type Future[T any] struct {
	ch        <-chan T
	errCh     <-chan error
	value     T
	err       error
	settled   bool
	mu        sync.RWMutex
	onSuccess []func(T)
	onError   []func(error)
	onFinally []func()
}

// Completer is used to complete a Future.
type Completer[T any] struct {
	Future *Future[T]
	ch     chan T
	errCh  chan error
}

// NewCompleter creates a new Completer.
func NewCompleter[T any]() *Completer[T] {
	ch := make(chan T, 1)
	errCh := make(chan error, 1)
	future := &Future[T]{
		ch:    ch,
		errCh: errCh,
	}
	completer := &Completer[T]{
		Future: future,
		ch:     ch,
		errCh:  errCh,
	}

	go func() {
		select {
		case val := <-future.ch:
			future.mu.Lock()
			if !future.settled {
				future.value = val
				future.settled = true
				for _, cb := range future.onSuccess {
					cb(val)
				}
			}
			future.mu.Unlock()
		case err := <-future.errCh:
			future.mu.Lock()
			if !future.settled {
				future.err = err
				future.settled = true
				for _, cb := range future.onError {
					cb(err)
				}
			}
			future.mu.Unlock()
		}
		future.mu.Lock()
		for _, cb := range future.onFinally {
			cb()
		}
		future.mu.Unlock()
	}()

	return completer
}

// NewFutureWithResolved creates a new Future that is already resolved.
func NewFutureWithResolved[T any](value T) *Future[T] {
	completer := NewCompleter[T]()
	completer.Resolve(value)
	return completer.Future
}

// NewFutureWithRejected creates a new Future that is already rejected.
func NewFutureWithRejected[T any](err error) *Future[T] {
	completer := NewCompleter[T]()
	completer.Reject(err)
	return completer.Future
}

// Resolve completes the Future with a value.
func (c *Completer[T]) Resolve(value T) {
	c.ch <- value
	close(c.ch)
	close(c.errCh)
}

// Reject completes the Future with an error.
func (c *Completer[T]) Reject(err error) {
	c.errCh <- err
	close(c.ch)
	close(c.errCh)
}

// Then attaches callbacks for the resolution and/or rejection of the Future.
func (f *Future[T]) Then(onSuccess func(T) *Future[any], onError func(error) *Future[any]) *Future[any] {
	completer := NewCompleter[any]()

	handleSuccess := func(val T) {
		if onSuccess != nil {
			onSuccess(val).Then(func(res any) *Future[any] {
				completer.Resolve(res)
				return nil
			}, func(err error) *Future[any] {
				completer.Reject(err)
				return nil
			})
		} else {
			completer.Resolve(val)
		}
	}

	handleError := func(err error) {
		if onError != nil {
			onError(err).Then(func(res any) *Future[any] {
				completer.Resolve(res)
				return nil
			}, func(err error) *Future[any] {
				completer.Reject(err)
				return nil
			})
		} else {
			completer.Reject(err)
		}
	}

	f.mu.Lock()
	if f.settled {
		if f.err != nil {
			handleError(f.err)
		} else {
			handleSuccess(f.value)
		}
	} else {
		f.onSuccess = append(f.onSuccess, handleSuccess)
		f.onError = append(f.onError, handleError)
	}
	f.mu.Unlock()

	return completer.Future
}

// Finally attaches a callback that is executed when the Future is settled.
func (f *Future[T]) Finally(onFinally func()) {
	f.mu.Lock()
	if f.settled {
		onFinally()
	} else {
		f.onFinally = append(f.onFinally, onFinally)
	}
	f.mu.Unlock()
}

// IsResolved checks if the future has been resolved successfully
func (f *Future[T]) IsResolved() bool {
	f.mu.RLock()
	defer f.mu.RUnlock()
	return f.settled && f.err == nil
}

// IsRejected checks if the future has been rejected with an error
func (f *Future[T]) IsRejected() bool {
	f.mu.RLock()
	defer f.mu.RUnlock()
	return f.settled && f.err != nil
}

// Value returns the resolved value of the Future. It should only be called
// after the Future is settled and IsResolved() is true.
func (f *Future[T]) Value() T {
	f.mu.RLock()
	defer f.mu.RUnlock()
	return f.value
}

// Err returns the rejection error of the Future. It should only be called
// after the Future is settled and IsRejected() is true.
func (f *Future[T]) Err() error {
	f.mu.RLock()
	defer f.mu.RUnlock()
	return f.err
}

// Throttler prevents accumulation of sequential async tasks
type Throttler struct {
	activePromise        *Future[any]
	queuedPromise        *Future[any]
	queuedPromiseFactory func() *Future[any]
	isDisposed           bool
	mutex                sync.Mutex
}

// NewThrottler creates a new Throttler
func NewThrottler() *Throttler {
	return &Throttler{}
}

// Queue queues a task to be executed by the throttler
func (t *Throttler) Queue(promiseFactory func() *Future[any]) *Future[any] {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if t.isDisposed {
		return NewFutureWithRejected[any](errors.New("Throttler is disposed"))
	}

	if t.activePromise != nil {
		t.queuedPromiseFactory = promiseFactory

		if t.queuedPromise == nil {
			onCompleteSuccess := func(interface{}) *Future[any] {
				t.mutex.Lock()
				defer t.mutex.Unlock()

				t.queuedPromise = nil
				if t.isDisposed {
					return nil
				}

				result := t.Queue(t.queuedPromiseFactory)
				t.queuedPromiseFactory = nil
				return result
			}

			onCompleteError := func(error) *Future[any] {
				t.mutex.Lock()
				defer t.mutex.Unlock()

				t.queuedPromise = nil
				if t.isDisposed {
					return nil
				}

				result := t.Queue(t.queuedPromiseFactory)
				t.queuedPromiseFactory = nil
				return result
			}

			completer := NewCompleter[any]()
			t.queuedPromise = completer.Future
			t.activePromise.Then(onCompleteSuccess, onCompleteError).Then(func(val interface{}) *Future[any] {
				completer.Resolve(val)
				return nil
			}, func(err error) *Future[any] {
				completer.Reject(err)
				return nil
			})
		}

		return t.queuedPromise
	}

	t.activePromise = promiseFactory()

	completer := NewCompleter[any]()
	t.activePromise.Then(func(result any) *Future[any] {
		t.mutex.Lock()
		t.activePromise = nil
		t.mutex.Unlock()
		completer.Resolve(result)
		return nil
	}, func(err error) *Future[any] {
		t.mutex.Lock()
		t.activePromise = nil
		t.mutex.Unlock()
		completer.Reject(err)
		return nil
	})

	return completer.Future
}

// Dispose disposes the throttler
func (t *Throttler) Dispose() {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	t.isDisposed = true
}

// Sequencer executes promises sequentially
type Sequencer struct {
	current *Future[any]
	mutex   sync.Mutex
}

// NewSequencer creates a new Sequencer
func NewSequencer() *Sequencer {
	return &Sequencer{
		current: NewFutureWithResolved[any](nil),
	}
}

// Queue queues a task to be executed sequentially
func (s *Sequencer) Queue(promiseTask func() *Future[any]) *Future[any] {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	newTaskSuccess := func(interface{}) *Future[any] {
		return promiseTask()
	}

	newTaskError := func(error) *Future[any] {
		return promiseTask()
	}

	s.current = s.current.Then(newTaskSuccess, newTaskError)
	return s.current
}

// SequencerByKey executes promises sequentially for a given key
type SequencerByKey[T comparable] struct {
	promiseMap map[T]*Future[any]
	mutex      sync.Mutex
}

// NewSequencerByKey creates a new SequencerByKey
func NewSequencerByKey[T comparable]() *SequencerByKey[T] {
	return &SequencerByKey[T]{
		promiseMap: make(map[T]*Future[any]),
	}
}

// Queue queues a task for a specific key to be executed sequentially
func (s *SequencerByKey[T]) Queue(key T, promiseTask func() *Future[any]) *Future[any] {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	runningPromise, exists := s.promiseMap[key]
	if !exists {
		runningPromise = NewFutureWithResolved[any](nil)
	}

	newPromise := runningPromise.Then(func(interface{}) *Future[any] {
		return promiseTask()
	}, func(error) *Future[any] {
		return promiseTask()
	})

	newPromise.Finally(func() {
		s.mutex.Lock()
		defer s.mutex.Unlock()
		if s.promiseMap[key] == newPromise {
			delete(s.promiseMap, key)
		}
	})

	s.promiseMap[key] = newPromise
	return newPromise
}

// ThrottledDelayer represents a throttled delayer
type ThrottledDelayer[T any] struct {
	defaultDelay time.Duration
	delayer      *Delayer
	throttler    *Throttler
	mutex        sync.Mutex
	lastPromise  *Future[T]
}

func NewThrottledDelayer[T any](defaultDelay time.Duration) *ThrottledDelayer[T] {
	return &ThrottledDelayer[T]{
		defaultDelay: defaultDelay,
		delayer:      NewDelayer(func() {}, 0),
		throttler:    NewThrottler(),
	}
}

// Trigger triggers the delayer with a task factory and optional delay
func (td *ThrottledDelayer[T]) Trigger(taskFactory func() *Future[T], delay ...time.Duration) *Future[T] {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	// Create new promise
	completer := NewCompleter[T]()
	td.lastPromise = completer.Future

	// Set delay
	delayDuration := td.defaultDelay
	if len(delay) > 0 {
		delayDuration = delay[0]
	}
	td.delayer.SetDelay(delayDuration)

	// Schedule task
	td.delayer.runner = func() {
		td.throttler.Queue(func() *Future[any] {
			result := taskFactory()
			result.Then(func(val T) *Future[any] {
				completer.Resolve(val)
				return nil
			}, func(err error) *Future[any] {
				completer.Reject(err)
				return nil
			})
			return result.Then(func(T) *Future[any] { return NewFutureWithResolved[any](nil) }, func(error) *Future[any] { return NewFutureWithResolved[any](nil) })
		})
	}
	td.delayer.Schedule(delayDuration)

	return completer.Future
}

// Dispose disposes the throttled delayer
func (td *ThrottledDelayer[T]) Dispose() {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	td.delayer.Dispose()
	td.throttler.Dispose()
}

// DeferredPromise represents a promise that can be resolved/rejected imperatively
type DeferredPromise[T any] struct {
	promise chan T
	errChan chan error
	value   T
	err     error
	settled bool
	mu      sync.RWMutex
}

// NewDeferredPromise creates a new deferred promise
func NewDeferredPromise[T any]() *DeferredPromise[T] {
	return &DeferredPromise[T]{
		promise: make(chan T, 1),
		errChan: make(chan error, 1),
	}
}

// Promise returns the promise channel
func (dp *DeferredPromise[T]) Promise() <-chan T {
	return dp.promise
}

// Error returns the error channel
func (dp *DeferredPromise[T]) Error() <-chan error {
	return dp.errChan
}

// Complete resolves the promise with a value
func (dp *DeferredPromise[T]) Complete(value T) {
	dp.mu.Lock()
	defer dp.mu.Unlock()

	if dp.settled {
		return
	}

	dp.value = value
	dp.settled = true
	dp.promise <- value
}

// Reject rejects the promise with an error
func (dp *DeferredPromise[T]) Reject(err error) {
	dp.mu.Lock()
	defer dp.mu.Unlock()

	if dp.settled {
		return
	}

	dp.err = err
	dp.settled = true
	dp.errChan <- err
}

// Cancel cancels the promise
func (dp *DeferredPromise[T]) Cancel() {
	dp.Reject(CancellationError{})
}

// IsSettled returns whether the promise is settled
func (dp *DeferredPromise[T]) IsSettled() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled
}

// IsResolved returns whether the promise is resolved
func (dp *DeferredPromise[T]) IsResolved() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled && dp.err == nil
}

// IsRejected returns whether the promise is rejected
func (dp *DeferredPromise[T]) IsRejected() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled && dp.err != nil
}

// Value returns the resolved value (if resolved)
func (dp *DeferredPromise[T]) Value() T {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.value
}

// Err returns the rejection error (if rejected)
func (dp *DeferredPromise[T]) Err() error {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.err
}

// IsThenable checks if an object implements a promise-like interface
func IsThenable(obj interface{}) bool {
	if obj == nil {
		return false
	}

	// Check if it has a Then method (simplified check)
	switch obj.(type) {
	case *Future[any]:
		return true
	case CancelablePromise[any]:
		return true
	case <-chan any:
		return true
	default:
		return false
	}
}

// RaceCancellationPromise races a promise with cancellation, returning default value on cancellation
func RaceCancellationPromise[T any](promise *Future[T], token CancellationToken, defaultValue ...T) *Future[T] {
	completer := NewCompleter[T]()

	// Listen for cancellation
	if token != nil {
		subscription := token.OnCancellationRequested().Subscribe(func(interface{}) {
			var result T
			if len(defaultValue) > 0 {
				result = defaultValue[0]
			}
			completer.Resolve(result)
		})

		// Clean up subscription when promise completes
		promise.Then(func(value T) *Future[any] {
			subscription.Dispose()
			completer.Resolve(value)
			return nil
		}, func(err error) *Future[any] {
			subscription.Dispose()
			completer.Reject(err)
			return nil
		})
	} else {
		// No cancellation token, just return the original promise
		return promise
	}

	return completer.Future
}

// RaceCancellationError races a promise with cancellation, returning CancellationError on cancellation
func RaceCancellationError[T any](promise *Future[T], token CancellationToken) *Future[T] {
	completer := NewCompleter[T]()

	// Listen for cancellation
	if token != nil {
		subscription := token.OnCancellationRequested().Subscribe(func(interface{}) {
			completer.Reject(CancellationError{})
		})

		// Clean up subscription when promise completes
		promise.Then(func(value T) *Future[any] {
			subscription.Dispose()
			completer.Resolve(value)
			return nil
		}, func(err error) *Future[any] {
			subscription.Dispose()
			completer.Reject(err)
			return nil
		})
	} else {
		// No cancellation token, just return the original promise
		return promise
	}

	return completer.Future
}

// Timeout creates a promise that resolves after the specified duration
func Timeout(duration time.Duration, token ...CancellationToken) *Future[interface{}] {
	completer := NewCompleter[interface{}]()

	timer := time.AfterFunc(duration, func() {
		completer.Resolve(nil)
	})

	// Handle cancellation if token provided
	if len(token) > 0 && token[0] != nil {
		subscription := token[0].OnCancellationRequested().Subscribe(func(interface{}) {
			timer.Stop()
			completer.Reject(CancellationError{})
		})

		// Clean up subscription when timer completes
		go func() {
			<-time.After(duration)
			subscription.Dispose()
		}()
	}

	return completer.Future
}

// AsPromise converts a callback function to a promise
func AsPromise[T any](callback func() T) *Future[T] {
	completer := NewCompleter[T]()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				if err, ok := r.(error); ok {
					completer.Reject(err)
				} else {
					completer.Reject(errors.New("panic occurred"))
				}
			}
		}()

		result := callback()
		completer.Resolve(result)
	}()

	return completer.Future
}

// SequencePromises runs promise factories sequentially and returns results in order
func SequencePromises[T any](promiseFactories []func() *Future[T]) *Future[[]T] {
	completer := NewCompleter[[]T]()

	go func() {
		results := make([]T, len(promiseFactories))

		for i, factory := range promiseFactories {
			promise := factory()

			// Wait for this promise to complete
			promise.Then(func(value T) *Future[any] {
				results[i] = value
				return nil
			}, func(err error) *Future[any] {
				completer.Reject(err)
				return nil
			})

			// Block until this promise is done
			for !promise.IsResolved() && !promise.IsRejected() {
				time.Sleep(time.Millisecond)
			}

			if promise.IsRejected() {
				return
			}
		}

		completer.Resolve(results)
	}()

	return completer.Future
}

//#region ResourceQueue

// ResourceQueue manages queues for resources to ensure sequential access
type ResourceQueue struct {
	queues map[string]*Sequencer
	mutex  sync.RWMutex
}

// NewResourceQueue creates a new ResourceQueue
func NewResourceQueue() *ResourceQueue {
	return &ResourceQueue{
		queues: make(map[string]*Sequencer),
	}
}

// QueueFor queues a task for a specific resource URI
// TypeScript equivalent: queueFor(resource: URI, factory: ITask<Promise<void>>, extUri: IExtUri = defaultExtUri): Promise<void>
func (rq *ResourceQueue) QueueFor(resource *URI, factory ITask[*Future[interface{}]], extUri IExtUri) *Future[interface{}] {
	// Handle default parameter for extUri (Go doesn't support default parameters)
	if extUri == nil {
		extUri = GetExtUri() // Use default extUri implementation
	}

	rq.mutex.Lock()
	defer rq.mutex.Unlock()

	// Use extUri.GetComparisonKey equivalent instead of uri.ToString()
	// This matches TypeScript: const key = extUri.getComparisonKey(resource);
	key := extUri.GetComparisonKey(resource)
	sequencer, exists := rq.queues[key]
	if !exists {
		sequencer = NewSequencer()
		rq.queues[key] = sequencer
	}

	// Queue the task with the sequencer
	return sequencer.Queue(func() *Future[any] {
		return factory()
	})
}

//#endregion
