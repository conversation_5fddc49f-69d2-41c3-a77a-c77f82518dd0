/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"context"
	"errors"
	"sync"
	"time"
)

// CancelablePromise represents a promise that can be cancelled
type CancelablePromise[T any] struct {
	ctx    context.Context
	cancel context.CancelFunc
	ch     chan T
	errCh  chan error
	mu     sync.Mutex
	done   bool
	result T
	err    error
}

// NewCancelablePromise creates a new cancelable promise
func NewCancelablePromise[T any](callback func(ctx context.Context) (T, error)) *CancelablePromise[T] {
	ctx, cancel := context.WithCancel(context.Background())
	cp := &CancelablePromise[T]{
		ctx:    ctx,
		cancel: cancel,
		ch:     make(chan T, 1),
		errCh:  make(chan error, 1),
	}

	go func() {
		defer close(cp.ch)
		defer close(cp.errCh)

		result, err := callback(ctx)
		cp.mu.Lock()
		cp.done = true
		cp.result = result
		cp.err = err
		cp.mu.Unlock()

		if err != nil {
			cp.errCh <- err
		} else {
			cp.ch <- result
		}
	}()

	return cp
}

// Cancel cancels the promise
func (cp *CancelablePromise[T]) Cancel() {
	cp.cancel()
}

// Wait waits for the promise to complete
func (cp *CancelablePromise[T]) Wait() (T, error) {
	select {
	case result := <-cp.ch:
		return result, nil
	case err := <-cp.errCh:
		var zero T
		return zero, err
	case <-cp.ctx.Done():
		var zero T
		return zero, errors.New("cancelled")
	}
}

// RaceCancellation races a channel against cancellation
func RaceCancellation[T any](ch <-chan T, token CancellationToken, defaultValue ...T) <-chan T {
	resultCh := make(chan T, 1)

	go func() {
		defer close(resultCh)

		var defaultVal T
		if len(defaultValue) > 0 {
			defaultVal = defaultValue[0]
		}

		select {
		case value, ok := <-ch:
			if ok {
				resultCh <- value
			} else {
				resultCh <- defaultVal
			}
		case <-token.OnCancellationRequested().Subscribe(func(interface{}) {}).(*disposableFunc).fn:
			resultCh <- defaultVal
		}
	}()

	return resultCh
}

// ITask represents a task that returns a value
type ITask[T any] interface {
	Execute() (T, error)
}

// TaskFunc is a function that implements ITask
type TaskFunc[T any] func() (T, error)

// Execute executes the task function
func (tf TaskFunc[T]) Execute() (T, error) {
	return tf()
}

// Throttler prevents accumulation of sequential async tasks
type Throttler struct {
	activePromise        *CancelablePromise[interface{}]
	queuedPromise        *CancelablePromise[interface{}]
	queuedPromiseFactory func() (interface{}, error)
	isDisposed           bool
	mu                   sync.Mutex
}

// NewThrottler creates a new throttler
func NewThrottler() *Throttler {
	return &Throttler{}
}

// Queue queues a task for execution
func (t *Throttler) Queue(promiseFactory func() (interface{}, error)) (interface{}, error) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.isDisposed {
		return nil, errors.New("throttler is disposed")
	}

	if t.activePromise != nil {
		t.queuedPromiseFactory = promiseFactory

		if t.queuedPromise == nil {
			t.queuedPromise = NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
				// Wait for active promise to complete
				_, err := t.activePromise.Wait()

				if ctx.Err() != nil {
					return nil, ctx.Err()
				}

				t.mu.Lock()
				factory := t.queuedPromiseFactory
				t.queuedPromiseFactory = nil
				t.queuedPromise = nil
				t.mu.Unlock()

				if factory != nil {
					return t.Queue(factory)
				}

				return nil, err
			})
		}

		return t.queuedPromise.Wait()
	}

	t.activePromise = NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
		return promiseFactory()
	})

	result, err := t.activePromise.Wait()
	t.mu.Lock()
	t.activePromise = nil
	t.mu.Unlock()

	return result, err
}

// Dispose disposes the throttler
func (t *Throttler) Dispose() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.isDisposed = true
}

// Sequencer executes tasks in sequence
type Sequencer struct {
	current chan interface{}
	mu      sync.Mutex
}

// NewSequencer creates a new sequencer
func NewSequencer() *Sequencer {
	ch := make(chan interface{}, 1)
	ch <- nil
	return &Sequencer{current: ch}
}

// Queue queues a task for sequential execution
func (s *Sequencer) Queue(promiseTask func() (interface{}, error)) (interface{}, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	resultCh := make(chan interface{}, 1)
	errCh := make(chan error, 1)

	go func() {
		// Wait for previous task to complete
		<-s.current

		// Execute the task
		result, err := promiseTask()

		if err != nil {
			errCh <- err
		} else {
			resultCh <- result
		}

		// Signal completion
		s.current <- nil
	}()

	select {
	case result := <-resultCh:
		return result, nil
	case err := <-errCh:
		return nil, err
	}
}

// SequencerByKey executes tasks in sequence per key
type SequencerByKey[TKey comparable] struct {
	promiseMap map[TKey]chan interface{}
	mu         sync.Mutex
}

// NewSequencerByKey creates a new sequencer by key
func NewSequencerByKey[TKey comparable]() *SequencerByKey[TKey] {
	return &SequencerByKey[TKey]{
		promiseMap: make(map[TKey]chan interface{}),
	}
}

// Queue queues a task for sequential execution by key
func (s *SequencerByKey[TKey]) Queue(key TKey, promiseTask func() (interface{}, error)) (interface{}, error) {
	s.mu.Lock()

	runningPromise, exists := s.promiseMap[key]
	if !exists {
		runningPromise = make(chan interface{}, 1)
		runningPromise <- nil
		s.promiseMap[key] = runningPromise
	}

	resultCh := make(chan interface{}, 1)
	errCh := make(chan error, 1)

	go func() {
		// Wait for previous task to complete
		<-runningPromise

		// Execute the task
		result, err := promiseTask()

		// Check if this is still the current promise for this key
		s.mu.Lock()
		if s.promiseMap[key] == runningPromise {
			delete(s.promiseMap, key)
		}
		s.mu.Unlock()

		if err != nil {
			errCh <- err
		} else {
			resultCh <- result
		}
	}()

	s.mu.Unlock()

	select {
	case result := <-resultCh:
		return result, nil
	case err := <-errCh:
		return nil, err
	}
}

// Keys returns an iterator of keys
func (s *SequencerByKey[TKey]) Keys() []TKey {
	s.mu.Lock()
	defer s.mu.Unlock()

	keys := make([]TKey, 0, len(s.promiseMap))
	for k := range s.promiseMap {
		keys = append(keys, k)
	}
	return keys
}

// IScheduledLater represents a scheduled task
type IScheduledLater interface {
	IsTriggered() bool
	Dispose()
}

// timeoutDeferred creates a timeout-based scheduled task
func timeoutDeferred(timeout time.Duration, fn func()) IScheduledLater {
	scheduled := true
	var mu sync.Mutex

	timer := time.AfterFunc(timeout, func() {
		mu.Lock()
		if scheduled {
			scheduled = false
			mu.Unlock()
			fn()
		} else {
			mu.Unlock()
		}
	})

	return &timeoutScheduled{
		timer:     timer,
		scheduled: &scheduled,
		mu:        &mu,
	}
}

// timeoutScheduled implements IScheduledLater
type timeoutScheduled struct {
	timer     *time.Timer
	scheduled *bool
	mu        *sync.Mutex
}

// IsTriggered returns true if the task is still scheduled
func (ts *timeoutScheduled) IsTriggered() bool {
	ts.mu.Lock()
	defer ts.mu.Unlock()
	return *ts.scheduled
}

// Dispose cancels the scheduled task
func (ts *timeoutScheduled) Dispose() {
	ts.mu.Lock()
	defer ts.mu.Unlock()
	*ts.scheduled = false
	if ts.timer != nil {
		ts.timer.Stop()
	}
}

// microtaskDeferred creates a microtask-based scheduled task
func microtaskDeferred(fn func()) IScheduledLater {
	scheduled := true
	var mu sync.Mutex

	go func() {
		time.Sleep(0) // Simulate microtask
		mu.Lock()
		if scheduled {
			scheduled = false
			mu.Unlock()
			fn()
		} else {
			mu.Unlock()
		}
	}()

	return &microtaskScheduled{
		scheduled: &scheduled,
		mu:        &mu,
	}
}

// microtaskScheduled implements IScheduledLater
type microtaskScheduled struct {
	scheduled *bool
	mu        *sync.Mutex
}

// IsTriggered returns true if the task is still scheduled
func (ms *microtaskScheduled) IsTriggered() bool {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	return *ms.scheduled
}

// Dispose cancels the scheduled task
func (ms *microtaskScheduled) Dispose() {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	*ms.scheduled = false
}

// Delayer delays execution of a task
type Delayer[T any] struct {
	defaultDelay      interface{} // time.Duration or *MicrotaskDelayType
	deferred          IScheduledLater
	completionPromise *CancelablePromise[T]
	doResolve         func(T)
	doReject          func(error)
	task              func() (T, error)
	mu                sync.Mutex
}

// NewDelayer creates a new delayer
func NewDelayer[T any](defaultDelay interface{}) *Delayer[T] {
	return &Delayer[T]{
		defaultDelay: defaultDelay,
	}
}

// Trigger triggers the delayed task
func (d *Delayer[T]) Trigger(task func() (T, error), delay ...interface{}) (T, error) {
	d.mu.Lock()
	defer d.mu.Unlock()

	d.task = task
	d.cancelTimeout()

	if d.completionPromise == nil {
		d.completionPromise = NewCancelablePromise[T](func(ctx context.Context) (T, error) {
			select {
			case <-ctx.Done():
				var zero T
				return zero, ctx.Err()
			default:
				if d.task != nil {
					taskToExecute := d.task
					d.task = nil
					return taskToExecute()
				}
				var zero T
				return zero, nil
			}
		})
	}

	actualDelay := d.defaultDelay
	if len(delay) > 0 {
		actualDelay = delay[0]
	}

	fn := func() {
		d.mu.Lock()
		d.deferred = nil
		if d.doResolve != nil {
			d.doResolve(d.task())
		}
		d.mu.Unlock()
	}

	if actualDelay == MicrotaskDelay {
		d.deferred = microtaskDeferred(fn)
	} else if duration, ok := actualDelay.(time.Duration); ok {
		d.deferred = timeoutDeferred(duration, fn)
	}

	return d.completionPromise.Wait()
}

// IsTriggered returns true if the delayer is triggered
func (d *Delayer[T]) IsTriggered() bool {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.deferred != nil && d.deferred.IsTriggered()
}

// Cancel cancels the delayer
func (d *Delayer[T]) Cancel() {
	d.mu.Lock()
	defer d.mu.Unlock()

	d.cancelTimeout()

	if d.completionPromise != nil {
		d.completionPromise.Cancel()
		d.completionPromise = nil
	}
}

// cancelTimeout cancels the current timeout
func (d *Delayer[T]) cancelTimeout() {
	if d.deferred != nil {
		d.deferred.Dispose()
		d.deferred = nil
	}
}

// Dispose disposes the delayer
func (d *Delayer[T]) Dispose() {
	d.Cancel()
}

// ThrottledDelayer combines throttling and delaying
type ThrottledDelayer[T any] struct {
	delayer   *Delayer[T]
	throttler *Throttler
}

// NewThrottledDelayer creates a new throttled delayer
func NewThrottledDelayer[T any](defaultDelay time.Duration) *ThrottledDelayer[T] {
	return &ThrottledDelayer[T]{
		delayer:   NewDelayer[T](defaultDelay),
		throttler: NewThrottler(),
	}
}

// Trigger triggers the throttled delayed task
func (td *ThrottledDelayer[T]) Trigger(promiseFactory func() (T, error), delay ...time.Duration) (T, error) {
	actualDelay := td.delayer.defaultDelay.(time.Duration)
	if len(delay) > 0 {
		actualDelay = delay[0]
	}

	return td.delayer.Trigger(func() (T, error) {
		result, err := td.throttler.Queue(func() (interface{}, error) {
			return promiseFactory()
		})
		if err != nil {
			var zero T
			return zero, err
		}
		return result.(T), nil
	}, actualDelay)
}

// IsTriggered returns true if the throttled delayer is triggered
func (td *ThrottledDelayer[T]) IsTriggered() bool {
	return td.delayer.IsTriggered()
}

// Cancel cancels the throttled delayer
func (td *ThrottledDelayer[T]) Cancel() {
	td.delayer.Cancel()
}

// Dispose disposes the throttled delayer
func (td *ThrottledDelayer[T]) Dispose() {
	td.delayer.Dispose()
	td.throttler.Dispose()
}

// Barrier is a synchronization primitive that starts closed and becomes open permanently
type Barrier struct {
	isOpen          bool
	promise         *CancelablePromise[bool]
	completePromise func(bool)
	mu              sync.Mutex
}

// NewBarrier creates a new barrier
func NewBarrier() *Barrier {
	b := &Barrier{
		isOpen: false,
	}

	b.promise = NewCancelablePromise[bool](func(ctx context.Context) (bool, error) {
		for {
			select {
			case <-ctx.Done():
				return false, ctx.Err()
			default:
				b.mu.Lock()
				if b.isOpen {
					b.mu.Unlock()
					return true, nil
				}
				b.mu.Unlock()
				time.Sleep(time.Millisecond)
			}
		}
	})

	return b
}

// IsOpen returns true if the barrier is open
func (b *Barrier) IsOpen() bool {
	b.mu.Lock()
	defer b.mu.Unlock()
	return b.isOpen
}

// Open opens the barrier
func (b *Barrier) Open() {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.isOpen = true
}

// Wait waits for the barrier to open
func (b *Barrier) Wait() (bool, error) {
	return b.promise.Wait()
}

// AutoOpenBarrier is a barrier that opens automatically after a timeout
type AutoOpenBarrier struct {
	*Barrier
	timer *time.Timer
}

// NewAutoOpenBarrier creates a new auto-opening barrier
func NewAutoOpenBarrier(autoOpenTimeMs time.Duration) *AutoOpenBarrier {
	b := &AutoOpenBarrier{
		Barrier: NewBarrier(),
	}

	b.timer = time.AfterFunc(autoOpenTimeMs, func() {
		b.Open()
	})

	return b
}

// Open opens the barrier and cancels the timer
func (ab *AutoOpenBarrier) Open() {
	if ab.timer != nil {
		ab.timer.Stop()
	}
	ab.Barrier.Open()
}

// Timeout creates a timeout promise
func Timeout(millis time.Duration, token ...CancellationToken) *CancelablePromise[interface{}] {
	if len(token) == 0 {
		return NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
			select {
			case <-time.After(millis):
				return nil, nil
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		})
	}

	return NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
		resultCh := make(chan interface{}, 1)
		errCh := make(chan error, 1)

		timer := time.AfterFunc(millis, func() {
			resultCh <- nil
		})

		dispose := token[0].OnCancellationRequested().Subscribe(func(interface{}) {
			timer.Stop()
			errCh <- errors.New("cancelled")
		})

		defer dispose.Dispose()

		select {
		case result := <-resultCh:
			return result, nil
		case err := <-errCh:
			return nil, err
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	})
}

// DisposableTimeout creates a disposable timeout
func DisposableTimeout(handler func(), timeout time.Duration, store ...*DisposableStore) IDisposable {
	timer := time.AfterFunc(timeout, func() {
		handler()
	})

	disposable := ToDisposable(func() {
		timer.Stop()
	})

	if len(store) > 0 && store[0] != nil {
		store[0].Add(disposable)
	}

	return disposable
}

// ILimiter interface for limiters
type ILimiter[T any] interface {
	Size() int
	Queue(factory func() (T, error)) (T, error)
	Clear()
}

// ILimitedTaskFactory represents a task factory for the limiter
type ILimitedTaskFactory[T any] struct {
	factory func() (T, error)
	c       chan T
	e       chan error
}

// Limiter limits the number of concurrent tasks
type Limiter[T any] struct {
	size                   int
	isDisposed             bool
	runningPromises        int
	maxDegreeOfParallelism int
	outstandingPromises    []*ILimitedTaskFactory[T]
	onDrained              *Emitter[interface{}]
	mu                     sync.Mutex
}

// NewLimiter creates a new limiter
func NewLimiter[T any](maxDegreeOfParallelism int) *Limiter[T] {
	return &Limiter[T]{
		maxDegreeOfParallelism: maxDegreeOfParallelism,
		outstandingPromises:    make([]*ILimitedTaskFactory[T], 0),
		onDrained:              NewEmitter[interface{}](),
	}
}

// Size returns the current size of the limiter
func (l *Limiter[T]) Size() int {
	l.mu.Lock()
	defer l.mu.Unlock()
	return l.size
}

// Queue queues a task for execution
func (l *Limiter[T]) Queue(factory func() (T, error)) (T, error) {
	l.mu.Lock()
	if l.isDisposed {
		l.mu.Unlock()
		var zero T
		return zero, errors.New("object has been disposed")
	}

	l.size++

	taskFactory := &ILimitedTaskFactory[T]{
		factory: factory,
		c:       make(chan T, 1),
		e:       make(chan error, 1),
	}

	l.outstandingPromises = append(l.outstandingPromises, taskFactory)
	l.consume()
	l.mu.Unlock()

	select {
	case result := <-taskFactory.c:
		return result, nil
	case err := <-taskFactory.e:
		var zero T
		return zero, err
	}
}

// consume processes queued tasks
func (l *Limiter[T]) consume() {
	for len(l.outstandingPromises) > 0 && l.runningPromises < l.maxDegreeOfParallelism {
		taskFactory := l.outstandingPromises[0]
		l.outstandingPromises = l.outstandingPromises[1:]
		l.runningPromises++

		go func(tf *ILimitedTaskFactory[T]) {
			result, err := tf.factory()
			if err != nil {
				tf.e <- err
			} else {
				tf.c <- result
			}

			l.consumed()
		}(taskFactory)
	}
}

// consumed is called when a task is completed
func (l *Limiter[T]) consumed() {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.isDisposed {
		return
	}

	l.runningPromises--
	l.size--

	if l.size == 0 {
		l.onDrained.Fire(nil)
	}

	if len(l.outstandingPromises) > 0 {
		l.consume()
	}
}

// Clear clears all outstanding tasks
func (l *Limiter[T]) Clear() {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.isDisposed {
		return
	}

	l.outstandingPromises = nil
	l.size = l.runningPromises
}

// WhenIdle returns a promise that resolves when the limiter is idle
func (l *Limiter[T]) WhenIdle() *CancelablePromise[interface{}] {
	if l.Size() > 0 {
		return NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
			disposable := l.onDrained.Subscribe(func(interface{}) {})
			defer disposable.Dispose()

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			default:
				return nil, nil
			}
		})
	}

	return NewCancelablePromise[interface{}](func(ctx context.Context) (interface{}, error) {
		return nil, nil
	})
}

// OnDrained returns the drained event
func (l *Limiter[T]) OnDrained() Event[interface{}] {
	return l.onDrained.Event()
}

// Dispose disposes the limiter
func (l *Limiter[T]) Dispose() {
	l.mu.Lock()
	defer l.mu.Unlock()

	l.isDisposed = true
	l.outstandingPromises = nil
	l.size = 0
	l.onDrained.Dispose()
}

// Queue is a limiter that processes one task at a time
type Queue[T any] struct {
	*Limiter[T]
}

// NewQueue creates a new queue
func NewQueue[T any]() *Queue[T] {
	return &Queue[T]{
		Limiter: NewLimiter[T](1),
	}
}

// TimeoutTimer represents a timer that can be cancelled
type TimeoutTimer struct {
	timer      *time.Timer
	isDisposed bool
	mu         sync.Mutex
}

// NewTimeoutTimer creates a new timeout timer
func NewTimeoutTimer(runner func(), timeout time.Duration) *TimeoutTimer {
	tt := &TimeoutTimer{}
	if runner != nil {
		tt.SetIfNotSet(runner, timeout)
	}
	return tt
}

// Dispose disposes the timer
func (tt *TimeoutTimer) Dispose() {
	tt.mu.Lock()
	defer tt.mu.Unlock()

	tt.Cancel()
	tt.isDisposed = true
}

// Cancel cancels the current timer
func (tt *TimeoutTimer) Cancel() {
	tt.mu.Lock()
	defer tt.mu.Unlock()

	if tt.timer != nil {
		tt.timer.Stop()
		tt.timer = nil
	}
}

// CancelAndSet cancels the current timer and sets a new one
func (tt *TimeoutTimer) CancelAndSet(runner func(), timeout time.Duration) {
	tt.mu.Lock()
	defer tt.mu.Unlock()

	if tt.isDisposed {
		return
	}

	tt.Cancel()
	tt.timer = time.AfterFunc(timeout, func() {
		tt.mu.Lock()
		tt.timer = nil
		tt.mu.Unlock()
		runner()
	})
}

// SetIfNotSet sets the timer if not already set
func (tt *TimeoutTimer) SetIfNotSet(runner func(), timeout time.Duration) {
	tt.mu.Lock()
	defer tt.mu.Unlock()

	if tt.isDisposed || tt.timer != nil {
		return
	}

	tt.timer = time.AfterFunc(timeout, func() {
		tt.mu.Lock()
		tt.timer = nil
		tt.mu.Unlock()
		runner()
	})
}

// RunOnceScheduler schedules a task to run once after a delay
type RunOnceScheduler struct {
	runner       func()
	timeout      time.Duration
	timeoutTimer *time.Timer
	mu           sync.Mutex
}

// NewRunOnceScheduler creates a new run-once scheduler
func NewRunOnceScheduler(runner func(), delay time.Duration) *RunOnceScheduler {
	return &RunOnceScheduler{
		runner:  runner,
		timeout: delay,
	}
}

// Dispose disposes the scheduler
func (ros *RunOnceScheduler) Dispose() {
	ros.mu.Lock()
	defer ros.mu.Unlock()

	ros.Cancel()
	ros.runner = nil
}

// Cancel cancels the scheduled task
func (ros *RunOnceScheduler) Cancel() {
	ros.mu.Lock()
	defer ros.mu.Unlock()

	if ros.timeoutTimer != nil {
		ros.timeoutTimer.Stop()
		ros.timeoutTimer = nil
	}
}

// Schedule schedules the task to run
func (ros *RunOnceScheduler) Schedule(delay ...time.Duration) {
	ros.mu.Lock()
	defer ros.mu.Unlock()

	ros.Cancel()

	actualDelay := ros.timeout
	if len(delay) > 0 {
		actualDelay = delay[0]
	}

	ros.timeoutTimer = time.AfterFunc(actualDelay, ros.onTimeout)
}

// Delay returns the current delay
func (ros *RunOnceScheduler) Delay() time.Duration {
	ros.mu.Lock()
	defer ros.mu.Unlock()
	return ros.timeout
}

// SetDelay sets the delay
func (ros *RunOnceScheduler) SetDelay(value time.Duration) {
	ros.mu.Lock()
	defer ros.mu.Unlock()
	ros.timeout = value
}

// IsScheduled returns true if the task is scheduled
func (ros *RunOnceScheduler) IsScheduled() bool {
	ros.mu.Lock()
	defer ros.mu.Unlock()
	return ros.timeoutTimer != nil
}

// Flush executes the task immediately if scheduled
func (ros *RunOnceScheduler) Flush() {
	ros.mu.Lock()
	defer ros.mu.Unlock()

	if ros.IsScheduled() {
		ros.Cancel()
		ros.doRun()
	}
}

// onTimeout is called when the timer expires
func (ros *RunOnceScheduler) onTimeout() {
	ros.mu.Lock()
	ros.timeoutTimer = nil
	runner := ros.runner
	ros.mu.Unlock()

	if runner != nil {
		runner()
	}
}

// doRun executes the runner
func (ros *RunOnceScheduler) doRun() {
	if ros.runner != nil {
		ros.runner()
	}
}

// Retry retries a task multiple times with a delay
func Retry[T any](task func() (T, error), delay time.Duration, retries int) (T, error) {
	var lastErr error

	for i := 0; i < retries; i++ {
		result, err := task()
		if err == nil {
			return result, nil
		}

		lastErr = err

		if i < retries-1 {
			time.Sleep(delay)
		}
	}

	var zero T
	return zero, lastErr
}

// Sequence runs promise factories in sequential order
func Sequence[T any](promiseFactories []func() (T, error)) ([]T, error) {
	results := make([]T, 0, len(promiseFactories))

	for _, factory := range promiseFactories {
		result, err := factory()
		if err != nil {
			return nil, err
		}
		results = append(results, result)
	}

	return results, nil
}

// First returns the first result that satisfies the condition
func First[T any](promiseFactories []func() (T, error), shouldStop func(T) bool, defaultValue T) (T, error) {
	if shouldStop == nil {
		shouldStop = func(t T) bool {
			// Default implementation for non-nil check
			return true
		}
	}

	for _, factory := range promiseFactories {
		result, err := factory()
		if err != nil {
			continue
		}

		if shouldStop(result) {
			return result, nil
		}
	}

	return defaultValue, nil
}

// This is a simplified version - the full implementation would include all the other utilities
// like AsyncIterableObject, DeferredPromise, etc. Due to the large size of the original file,
// I'm including the most essential parts here.
