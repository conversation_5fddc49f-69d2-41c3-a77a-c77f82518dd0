/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// MicrotaskDelay is a special value that can be passed to indicate using microtask scheduling
type MicrotaskDelayType struct{}

// MicrotaskDelay is the singleton instance for microtask delay
var MicrotaskDelay = &MicrotaskDelayType{}
