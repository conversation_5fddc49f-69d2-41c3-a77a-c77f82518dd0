/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"math/rand"
	"sync"
)

// SkipListNode represents a node in the skip list
type SkipListNode[K, V any] struct {
	key     K
	value   V
	forward []*SkipListNode[K, V]
}

// SkipList represents a skip list data structure
type SkipList[K, V any] struct {
	header   *SkipListNode[K, V]
	level    int
	size     int
	maxLevel int
	compare  func(K, K) int
	limit    int
	mutex    sync.RWMutex
}

// NewSkipList creates a new skip list with the given comparison function and limit
func NewSkipList[K, V any](compare func(K, K) int, limit int) *SkipList[K, V] {
	maxLevel := 16 // reasonable default
	header := &SkipListNode[K, V]{
		forward: make([]*SkipListNode[K, V], maxLevel),
	}
	
	return &SkipList[K, V]{
		header:   header,
		level:    0,
		size:     0,
		maxLevel: maxLevel,
		compare:  compare,
		limit:    limit,
	}
}

// randomLevel generates a random level for a new node
func (sl *SkipList[K, V]) randomLevel() int {
	level := 0
	for rand.Float32() < 0.5 && level < sl.maxLevel-1 {
		level++
	}
	return level
}

// Set inserts or updates a key-value pair in the skip list
func (sl *SkipList[K, V]) Set(key K, value V) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()
	
	update := make([]*SkipListNode[K, V], sl.maxLevel)
	current := sl.header
	
	// Find the position to insert/update
	for i := sl.level; i >= 0; i-- {
		for current.forward[i] != nil && sl.compare(current.forward[i].key, key) < 0 {
			current = current.forward[i]
		}
		update[i] = current
	}
	
	current = current.forward[0]
	
	// If key already exists, update the value
	if current != nil && sl.compare(current.key, key) == 0 {
		current.value = value
		return
	}
	
	// Check if we need to remove oldest entries due to limit
	if sl.limit > 0 && sl.size >= sl.limit {
		sl.removeOldest()
	}
	
	// Insert new node
	newLevel := sl.randomLevel()
	if newLevel > sl.level {
		for i := sl.level + 1; i <= newLevel; i++ {
			update[i] = sl.header
		}
		sl.level = newLevel
	}
	
	newNode := &SkipListNode[K, V]{
		key:     key,
		value:   value,
		forward: make([]*SkipListNode[K, V], newLevel+1),
	}
	
	for i := 0; i <= newLevel; i++ {
		newNode.forward[i] = update[i].forward[i]
		update[i].forward[i] = newNode
	}
	
	sl.size++
}

// Get retrieves a value by key from the skip list
func (sl *SkipList[K, V]) Get(key K) (V, bool) {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()
	
	current := sl.header
	
	for i := sl.level; i >= 0; i-- {
		for current.forward[i] != nil && sl.compare(current.forward[i].key, key) < 0 {
			current = current.forward[i]
		}
	}
	
	current = current.forward[0]
	
	if current != nil && sl.compare(current.key, key) == 0 {
		return current.value, true
	}
	
	var zero V
	return zero, false
}

// Delete removes a key-value pair from the skip list
func (sl *SkipList[K, V]) Delete(key K) bool {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()
	
	update := make([]*SkipListNode[K, V], sl.maxLevel)
	current := sl.header
	
	for i := sl.level; i >= 0; i-- {
		for current.forward[i] != nil && sl.compare(current.forward[i].key, key) < 0 {
			current = current.forward[i]
		}
		update[i] = current
	}
	
	current = current.forward[0]
	
	if current == nil || sl.compare(current.key, key) != 0 {
		return false
	}
	
	// Remove the node
	for i := 0; i <= sl.level; i++ {
		if update[i].forward[i] != current {
			break
		}
		update[i].forward[i] = current.forward[i]
	}
	
	// Update level
	for sl.level > 0 && sl.header.forward[sl.level] == nil {
		sl.level--
	}
	
	sl.size--
	return true
}

// removeOldest removes the oldest (first) entry from the skip list
func (sl *SkipList[K, V]) removeOldest() {
	if sl.size == 0 {
		return
	}
	
	first := sl.header.forward[0]
	if first == nil {
		return
	}
	
	sl.Delete(first.key)
}

// Size returns the number of elements in the skip list
func (sl *SkipList[K, V]) Size() int {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()
	return sl.size
}

// Clear removes all elements from the skip list
func (sl *SkipList[K, V]) Clear() {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()
	
	for i := 0; i < sl.maxLevel; i++ {
		sl.header.forward[i] = nil
	}
	sl.level = 0
	sl.size = 0
}

// Entries returns all key-value pairs in the skip list
func (sl *SkipList[K, V]) Entries() [][2]interface{} {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()
	
	entries := make([][2]interface{}, 0, sl.size)
	current := sl.header.forward[0]
	
	for current != nil {
		entries = append(entries, [2]interface{}{current.key, current.value})
		current = current.forward[0]
	}
	
	return entries
}
