/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

//#region rimraf

// RimRafMode represents the mode for recursive deletion
type RimRafMode int

const (
	// RimRafModeUnlink - Slow version that unlinks each file and folder
	RimRafModeUnlink RimRafMode = iota

	// RimRafModeMove - Fast version that first moves the file/folder
	// into a temp directory and then deletes that without waiting for it
	RimRafModeMove
)

// Rimraf allows to delete the provided path (either file or folder) recursively
// with the options:
//   - UNLINK: direct removal from disk
//   - MOVE: faster variant that first moves the target to temp dir and then
//     deletes it in the background without waiting for that to finish.
//     the optional moveToPath allows to override where to rename the
//     path to before deleting it.
func Rimraf(path string, mode RimRafMode, moveToPath ...string) error {
	if basecommon.IsRootOrDriveLetter(path) {
		return fmt.Errorf("rimraf - will refuse to recursively delete root")
	}

	// delete: via rm
	if mode == RimRafModeUnlink {
		return rimrafUnlink(path)
	}

	// delete: via move
	var targetPath string
	if len(moveToPath) > 0 && moveToPath[0] != "" {
		targetPath = moveToPath[0]
	} else {
		targetPath = basecommon.RandomPath(os.TempDir(), "", 8)
	}
	return rimrafMove(path, targetPath)
}

func rimrafMove(path string, moveToPath string) error {
	// Try to rename first
	err := os.Rename(path, moveToPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // ignore - path to delete did not exist
		}
		return rimrafUnlink(path) // otherwise fallback to unlink
	}

	// Delete but do not return as promise - fire and forget
	go func() {
		rimrafUnlink(moveToPath) // ignore errors in background deletion
	}()

	return nil
}

func rimrafUnlink(path string) error {
	return os.RemoveAll(path)
}

//#endregion

//#region readdir with NFC support (macos)

// IDirent represents a directory entry with file type information
type IDirent struct {
	Name string

	isFile         bool
	isDirectory    bool
	isSymbolicLink bool
}

// IsFile returns true if the entry is a file
func (d *IDirent) IsFile() bool {
	return d.isFile
}

// IsDirectory returns true if the entry is a directory
func (d *IDirent) IsDirectory() bool {
	return d.isDirectory
}

// IsSymbolicLink returns true if the entry is a symbolic link
func (d *IDirent) IsSymbolicLink() bool {
	return d.isSymbolicLink
}

// Readdir is a drop-in replacement of os.ReadDir with support
// for converting from macOS NFD unicode form to NFC
// (https://github.com/nodejs/node/issues/2165)
func Readdir(path string) ([]string, error) {
	entries, err := os.ReadDir(path)
	if err != nil {
		return nil, err
	}

	names := make([]string, len(entries))
	for i, entry := range entries {
		names[i] = entry.Name()
	}

	return handleDirectoryChildrenStrings(names), nil
}

// ReaddirWithFileTypes reads directory entries with file type information
func ReaddirWithFileTypes(path string) ([]*IDirent, error) {
	entries, err := safeReaddirWithFileTypes(path)
	if err != nil {
		return nil, err
	}

	return handleDirectoryChildrenDirents(entries), nil
}

func safeReaddirWithFileTypes(path string) ([]*IDirent, error) {
	entries, err := os.ReadDir(path)
	if err != nil {
		fmt.Printf("[node.js fs] readdir with filetypes failed with error: %v\n", err)

		// Fallback to manually reading and resolving each
		// children of the folder in case we hit an error
		// previously.
		// This can only really happen on exotic file systems
		// such as explained in #115645 where we get entries
		// from readdir that we can later not lstat.
		result := []*IDirent{}
		children, err := Readdir(path)
		if err != nil {
			return nil, err
		}

		for _, child := range children {
			isFile := false
			isDirectory := false
			isSymbolicLink := false

			childPath := filepath.Join(path, child)
			if info, err := os.Lstat(childPath); err == nil {
				isFile = info.Mode().IsRegular()
				isDirectory = info.IsDir()
				isSymbolicLink = info.Mode()&os.ModeSymlink != 0
			} else {
				fmt.Printf("[node.js fs] unexpected error from lstat after readdir: %v\n", err)
			}

			result = append(result, &IDirent{
				Name:           child,
				isFile:         isFile,
				isDirectory:    isDirectory,
				isSymbolicLink: isSymbolicLink,
			})
		}

		return result, nil
	}

	result := make([]*IDirent, len(entries))
	for i, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			// Handle error gracefully
			result[i] = &IDirent{
				Name:           entry.Name(),
				isFile:         false,
				isDirectory:    false,
				isSymbolicLink: false,
			}
			continue
		}

		result[i] = &IDirent{
			Name:           entry.Name(),
			isFile:         info.Mode().IsRegular(),
			isDirectory:    info.IsDir(),
			isSymbolicLink: info.Mode()&os.ModeSymlink != 0,
		}
	}

	return result, nil
}

func handleDirectoryChildrenStrings(children []string) []string {
	result := make([]string, len(children))
	for i, child := range children {
		// Mac: uses NFD unicode form on disk, but we want NFC
		// See also https://github.com/nodejs/node/issues/2165
		if basecommon.IsMacintosh {
			result[i] = basecommon.NormalizeNFC(child)
		} else {
			result[i] = child
		}
	}
	return result
}

func handleDirectoryChildrenDirents(children []*IDirent) []*IDirent {
	for _, child := range children {
		// Mac: uses NFD unicode form on disk, but we want NFC
		// See also https://github.com/nodejs/node/issues/2165
		if basecommon.IsMacintosh {
			child.Name = basecommon.NormalizeNFC(child.Name)
		}
	}
	return children
}

// ReadDirsInDir is a convenience method to read all children of a path that
// are directories.
func ReadDirsInDir(dirPath string) ([]string, error) {
	children, err := Readdir(dirPath)
	if err != nil {
		return nil, err
	}

	directories := []string{}
	for _, child := range children {
		childPath := filepath.Join(dirPath, child)
		if exists, err := SymlinkSupport.ExistsDirectory(childPath); err == nil && exists {
			directories = append(directories, child)
		}
	}

	return directories, nil
}

//#endregion

//#region whenDeleted()

// WhenDeleted returns a channel that will be closed when the provided path
// is deleted from disk.
func WhenDeleted(path string, intervalMs ...int) <-chan struct{} {
	interval := 1000 * time.Millisecond
	if len(intervalMs) > 0 && intervalMs[0] > 0 {
		interval = time.Duration(intervalMs[0]) * time.Millisecond
	}

	done := make(chan struct{})

	go func() {
		defer close(done)

		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		var running bool
		var mutex sync.Mutex

		for {
			select {
			case <-ticker.C:
				mutex.Lock()
				if !running {
					running = true
					go func() {
						defer func() {
							mutex.Lock()
							running = false
							mutex.Unlock()
						}()

						if _, err := os.Stat(path); os.IsNotExist(err) {
							done <- struct{}{}
							return
						}
					}()
				}
				mutex.Unlock()
			case <-done:
				return
			}
		}
	}()

	return done
}

//#endregion

//#region Methods with symbolic links support

// SymlinkSupport provides methods for working with symbolic links
var SymlinkSupport = struct {
	Stat            func(path string) (*IStats, error)
	ExistsFile      func(path string) (bool, error)
	ExistsDirectory func(path string) (bool, error)
}{
	Stat:            symlinkStat,
	ExistsFile:      symlinkExistsFile,
	ExistsDirectory: symlinkExistsDirectory,
}

// IStats represents file stats with symbolic link information
type IStats struct {
	// The stats of the file. If the file is a symbolic
	// link, the stats will be of that target file and
	// not the link itself.
	// If the file is a symbolic link pointing to a non
	// existing file, the stat will be of the link and
	// the Dangling flag will indicate this.
	Stat os.FileInfo

	// Will be provided if the resource is a symbolic link
	// on disk. Use the Dangling flag to find out if it
	// points to a resource that does not exist on disk.
	SymbolicLink *struct {
		Dangling bool
	}
}

// symlinkStat resolves the os.FileInfo of the provided path. If the path is a
// symbolic link, the os.FileInfo will be from the target it points
// to. If the target does not exist, Dangling: true will be returned
// as SymbolicLink value.
func symlinkStat(path string) (*IStats, error) {
	// First stat the link
	var lstats os.FileInfo
	var isSymlink bool

	if info, err := os.Lstat(path); err == nil {
		lstats = info
		isSymlink = info.Mode()&os.ModeSymlink != 0

		// Return early if the stat is not a symbolic link at all
		if !isSymlink {
			return &IStats{Stat: lstats}, nil
		}
	}

	// If the stat is a symbolic link or failed to stat, use os.Stat()
	// which for symbolic links will stat the target they point to
	if stats, err := os.Stat(path); err == nil {
		symbolicLink := (*struct{ Dangling bool })(nil)
		if isSymlink {
			symbolicLink = &struct{ Dangling bool }{Dangling: false}
		}
		return &IStats{Stat: stats, SymbolicLink: symbolicLink}, nil
	} else {
		// If the link points to a nonexistent file we still want
		// to return it as result while setting dangling: true flag
		if os.IsNotExist(err) && lstats != nil {
			return &IStats{
				Stat:         lstats,
				SymbolicLink: &struct{ Dangling bool }{Dangling: true},
			}, nil
		}

		// Windows: workaround a node.js bug where reparse points
		// are not supported (https://github.com/nodejs/node/issues/36790)
		if basecommon.IsWindows && isAccessError(err) {
			if linkTarget, err := os.Readlink(path); err == nil {
				if stats, err := os.Stat(linkTarget); err == nil {
					return &IStats{
						Stat:         stats,
						SymbolicLink: &struct{ Dangling bool }{Dangling: false},
					}, nil
				} else {
					// If the link points to a nonexistent file we still want
					// to return it as result while setting dangling: true flag
					if os.IsNotExist(err) && lstats != nil {
						return &IStats{
							Stat:         lstats,
							SymbolicLink: &struct{ Dangling bool }{Dangling: true},
						}, nil
					}
					return nil, err
				}
			}
		}

		return nil, err
	}
}

// symlinkExistsFile figures out if the path exists and is a file with support
// for symlinks.
//
// Note: this will return false for a symlink that exists on
// disk but is dangling (pointing to a nonexistent path).
//
// Use os.Stat if you only care about the path existing on disk
// or not without support for symbolic links.
func symlinkExistsFile(path string) (bool, error) {
	stats, err := symlinkStat(path)
	if err != nil {
		return false, nil // Ignore error, path might not exist
	}

	return stats.Stat.Mode().IsRegular() && (stats.SymbolicLink == nil || !stats.SymbolicLink.Dangling), nil
}

// symlinkExistsDirectory figures out if the path exists and is a directory with support for
// symlinks.
//
// Note: this will return false for a symlink that exists on
// disk but is dangling (pointing to a nonexistent path).
//
// Use os.Stat if you only care about the path existing on disk
// or not without support for symbolic links.
func symlinkExistsDirectory(path string) (bool, error) {
	stats, err := symlinkStat(path)
	if err != nil {
		return false, nil // Ignore error, path might not exist
	}

	return stats.Stat.IsDir() && (stats.SymbolicLink == nil || !stats.SymbolicLink.Dangling), nil
}

// isAccessError checks if the error is an access-related error
func isAccessError(err error) bool {
	// This is a simplified check - in a real implementation you might want
	// to check for specific error types or error codes
	return err != nil && (os.IsPermission(err) || err.Error() == "access is denied")
}

//#endregion

//#region Write File

// According to node.js docs (https://nodejs.org/docs/v14.16.0/api/fs.html#fs_fs_writefile_file_data_options_callback)
// it is not safe to call writeFile() on the same path multiple times without waiting for the callback to return.
// Therefore we use a Queue on the path that is given to us to sequentialize calls to the same path properly.
var writeQueues = basecommon.NewResourceQueue()

// IWriteFileOptions represents options for writing files
type IWriteFileOptions struct {
	Mode *os.FileMode
	Flag *int
}

// IEnsuredWriteFileOptions represents ensured write file options with defaults
type IEnsuredWriteFileOptions struct {
	Mode os.FileMode
	Flag int
}

var canFlush = true

// ConfigureFlushOnWrite configures whether to flush on write
func ConfigureFlushOnWrite(enabled bool) {
	canFlush = enabled
}

// WriteFile is the same as os.WriteFile but with an additional call to
// fsync after writing to ensure changes are flushed to disk.
//
// In addition, multiple writes to the same path are queued.
func WriteFile(path string, data []byte, options *IWriteFileOptions) error {
	uri := basecommon.File(path)

	// Create a task that returns a Future
	task := basecommon.ITask[*basecommon.Future[interface{}]](func() *basecommon.Future[interface{}] {
		completer := basecommon.NewCompleter[interface{}]()
		go func() {
			ensuredOptions := ensureWriteOptions(options)
			err := doWriteFileAndFlush(path, data, ensuredOptions)
			if err != nil {
				completer.Reject(err)
			} else {
				completer.Resolve(nil)
			}
		}()
		return completer.Future
	})

	// Queue the task using the new signature
	future := writeQueues.QueueFor(uri, task, nil) // nil uses default extUri

	// Wait for the result using a channel
	resultChan := make(chan error, 1)

	future.Then(func(result interface{}) *basecommon.Future[any] {
		// Check if the result is an error
		if resultErr, ok := result.(error); ok {
			resultChan <- resultErr
		} else {
			resultChan <- nil
		}
		return nil
	}, func(err error) *basecommon.Future[any] {
		resultChan <- err
		return nil
	})

	return <-resultChan
}

// WriteFileSync is the same as os.WriteFile but with an additional call to
// fsync after writing to ensure changes are flushed to disk.
//
// Deprecated: always prefer async variants over sync!
func WriteFileSync(path string, data []byte, options *IWriteFileOptions) error {
	ensuredOptions := ensureWriteOptions(options)

	if !canFlush {
		return os.WriteFile(path, data, ensuredOptions.Mode)
	}

	// Open the file with same flags and mode as os.WriteFile()
	file, err := os.OpenFile(path, ensuredOptions.Flag, ensuredOptions.Mode)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write the data
	if _, err := file.Write(data); err != nil {
		return err
	}

	// Flush contents (not metadata) of the file to disk
	if err := file.Sync(); err != nil {
		fmt.Printf("[node.js fs] fdatasync is now disabled for this session because it failed: %v\n", err)
		ConfigureFlushOnWrite(false)
	}

	return nil
}

// doWriteFileAndFlush calls os.WriteFile() followed by a fsync() call to flush the changes to disk
// We do this in cases where we want to make sure the data is really on disk and
// not in some cache.
func doWriteFileAndFlush(path string, data []byte, options IEnsuredWriteFileOptions) error {
	if !canFlush {
		return os.WriteFile(path, data, options.Mode)
	}

	// Open the file with same flags and mode as os.WriteFile()
	file, err := os.OpenFile(path, options.Flag, options.Mode)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write the data
	if _, err := file.Write(data); err != nil {
		return err
	}

	// Flush contents (not metadata) of the file to disk
	// https://github.com/microsoft/vscode/issues/9589
	if err := file.Sync(); err != nil {
		// In some exotic setups it is well possible that Go fails to sync
		// In that case we disable flushing and warn to the console
		fmt.Printf("[node.js fs] fdatasync is now disabled for this session because it failed: %v\n", err)
		ConfigureFlushOnWrite(false)
	}

	return nil
}

func ensureWriteOptions(options *IWriteFileOptions) IEnsuredWriteFileOptions {
	if options == nil {
		return IEnsuredWriteFileOptions{
			Mode: 0o666, // default node.js mode for files
			Flag: os.O_WRONLY | os.O_CREATE | os.O_TRUNC,
		}
	}

	mode := os.FileMode(0o666) // default node.js mode for files
	if options.Mode != nil {
		mode = *options.Mode
	}

	flag := os.O_WRONLY | os.O_CREATE | os.O_TRUNC // default 'w' flag
	if options.Flag != nil {
		flag = *options.Flag
	}

	return IEnsuredWriteFileOptions{
		Mode: mode,
		Flag: flag,
	}
}

//#endregion

//#region Move / Copy

// Rename is a drop-in replacement for os.Rename that:
// - allows to move across multiple disks
// - attempts to retry the operation for certain error codes on Windows
func Rename(source, target string, windowsRetryTimeout ...int) error {
	if source == target {
		return nil // simulate node.js behaviour here and do a no-op if paths match
	}

	timeout := 60000 // default 60 seconds
	if len(windowsRetryTimeout) > 0 && windowsRetryTimeout[0] >= 0 {
		timeout = windowsRetryTimeout[0]
	}

	if basecommon.IsWindows && timeout > 0 {
		// On Windows, a rename can fail when either source or target
		// is locked by AV software.
		return renameWithRetry(source, target, time.Now(), time.Duration(timeout)*time.Millisecond, 0)
	} else {
		err := os.Rename(source, target)
		if err != nil {
			// In two cases we fallback to classic copy and delete:
			//
			// 1.) The EXDEV error indicates that source and target are on different devices
			// In this case, fallback to using a copy() operation as there is no way to
			// rename() between different devices.
			//
			// 2.) The user tries to rename a file/folder that ends with a dot. This is not
			// really possible to move then, at least on UNC devices.
			if isExdevError(err) || (strings.ToLower(source) != strings.ToLower(target) && strings.HasSuffix(source, ".")) {
				if err := Copy(source, target, &CopyOptions{PreserveSymlinks: false}); err != nil {
					return err
				}
				return Rimraf(source, RimRafModeMove)
			}
			return err
		}
	}

	return nil
}

func renameWithRetry(source, target string, startTime time.Time, retryTimeout time.Duration, attempt int) error {
	err := os.Rename(source, target)
	if err == nil {
		return nil
	}

	// Only retry for errors we think are temporary
	if !isTemporaryError(err) {
		return err
	}

	if time.Since(startTime) >= retryTimeout {
		fmt.Printf("[node.js fs] rename failed after %d retries with error: %v\n", attempt, err)
		return err // give up after configurable timeout
	}

	if attempt == 0 {
		abortRetry := false
		if stats, err := SymlinkSupport.Stat(target); err == nil {
			if !stats.Stat.Mode().IsRegular() {
				abortRetry = true // if target is not a file, EPERM error may be raised and we should not attempt to retry
			}
		}

		if abortRetry {
			return err
		}
	}

	// Delay with incremental backoff up to 100ms
	delay := time.Duration(min(100, attempt*10)) * time.Millisecond
	time.Sleep(delay)

	// Attempt again
	return renameWithRetry(source, target, startTime, retryTimeout, attempt+1)
}

// CopyOptions represents options for copying files/directories
type CopyOptions struct {
	PreserveSymlinks bool
}

// ICopyPayload represents the payload for copy operations
type ICopyPayload struct {
	Root               CopyRoot
	Options            *CopyOptions
	HandledSourcePaths map[string]bool
}

// CopyRoot represents the root source and target for copy operations
type CopyRoot struct {
	Source string
	Target string
}

// Copy recursively copies all of source to target.
//
// The options PreserveSymlinks configures how symbolic
// links should be handled when encountered. Set to
// false to not preserve them and true otherwise.
func Copy(source, target string, options *CopyOptions) error {
	if options == nil {
		options = &CopyOptions{PreserveSymlinks: true}
	}

	payload := &ICopyPayload{
		Root:               CopyRoot{Source: source, Target: target},
		Options:            options,
		HandledSourcePaths: make(map[string]bool),
	}

	return doCopy(source, target, payload)
}

// When copying a file or folder, we want to preserve the mode
// it had and as such provide it when creating. However, modes
// can go beyond what we expect (see link below), so we mask it.
// (https://github.com/nodejs/node-v0.x-archive/issues/3045#issuecomment-4862588)
const copyModeMask = 0o777

func doCopy(source, target string, payload *ICopyPayload) error {
	// Keep track of paths already copied to prevent
	// cycles from symbolic links to cause issues
	if payload.HandledSourcePaths[source] {
		return nil
	}
	payload.HandledSourcePaths[source] = true

	stats, err := SymlinkSupport.Stat(source)
	if err != nil {
		return err
	}

	// Symlink
	if stats.SymbolicLink != nil {
		// Try to re-create the symlink unless PreserveSymlinks: false
		if payload.Options.PreserveSymlinks {
			if err := doCopySymlink(source, target, payload); err == nil {
				return nil
			}
			// in any case of an error fallback to normal copy via dereferencing
		}

		if stats.SymbolicLink.Dangling {
			return nil // skip dangling symbolic links from here on (https://github.com/microsoft/vscode/issues/111621)
		}
	}

	// Folder
	if stats.Stat.IsDir() {
		return doCopyDirectory(source, target, stats.Stat.Mode()&copyModeMask, payload)
	}

	// File or file-like
	return doCopyFile(source, target, stats.Stat.Mode()&copyModeMask)
}

func doCopyDirectory(source, target string, mode os.FileMode, payload *ICopyPayload) error {
	// Create folder
	if err := os.MkdirAll(target, mode); err != nil {
		return err
	}

	// Copy each file recursively
	files, err := Readdir(source)
	if err != nil {
		return err
	}

	for _, file := range files {
		sourcePath := filepath.Join(source, file)
		targetPath := filepath.Join(target, file)
		if err := doCopy(sourcePath, targetPath, payload); err != nil {
			return err
		}
	}

	return nil
}

func doCopyFile(source, target string, mode os.FileMode) error {
	// Copy file
	sourceFile, err := os.Open(source)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	targetFile, err := os.OpenFile(target, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, mode)
	if err != nil {
		return err
	}
	defer targetFile.Close()

	if _, err := targetFile.ReadFrom(sourceFile); err != nil {
		return err
	}

	// restore mode (https://github.com/nodejs/node/issues/1104)
	return os.Chmod(target, mode)
}

func doCopySymlink(source, target string, payload *ICopyPayload) error {
	// Figure out link target
	linkTarget, err := os.Readlink(source)
	if err != nil {
		return err
	}

	// Special case: the symlink points to a target that is
	// actually within the path that is being copied. In that
	// case we want the symlink to point to the target and
	// not the source
	if basecommon.IsEqualOrParent(linkTarget, payload.Root.Source, !basecommon.IsLinux) {
		relativePath, err := filepath.Rel(payload.Root.Source, linkTarget)
		if err != nil {
			return err
		}
		linkTarget = filepath.Join(payload.Root.Target, relativePath)
	}

	// Create symlink
	return os.Symlink(linkTarget, target)
}

// Helper functions

func isExdevError(err error) bool {
	// Check if error indicates cross-device operation
	return err != nil && strings.Contains(err.Error(), "cross-device")
}

func isTemporaryError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "access is denied") ||
		strings.Contains(errStr, "permission denied") ||
		strings.Contains(errStr, "resource busy")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

//#endregion

//#region Path resolvers

// Realcase returns the exact path that the file has on disk given an absolute, normalized, and existing file path.
// On a case insensitive file system, the returned path might differ from the original
// path by character casing.
// On a case sensitive file system, the returned path will always be identical to the
// original path.
// In case of errors, empty string is returned. But you cannot use this function to verify that
// a path exists.
//
// realcase does not handle '..' or '.' path segments and it does not take the locale into account.
func Realcase(path string, token ...basecommon.CancellationToken) (string, error) {
	if basecommon.IsLinux {
		// This method is unsupported on OS that have case sensitive
		// file system where the same path can exist in different forms
		// (see also https://github.com/microsoft/vscode/issues/139709)
		return path, nil
	}

	dir := filepath.Dir(path)
	if path == dir { // end recursion
		return path, nil
	}

	name := strings.ToLower(filepath.Base(path))
	if name == "" {
		name = strings.ToLower(path) // for windows drive letters
	}

	// Check for cancellation
	if len(token) > 0 && token[0] != nil && token[0].IsCancellationRequested() {
		return "", fmt.Errorf("operation was cancelled")
	}

	entries, err := Readdir(dir)
	if err != nil {
		return "", nil // silently ignore error
	}

	// Use a case insensitive search
	var found []string
	for _, entry := range entries {
		if strings.ToLower(entry) == name {
			found = append(found, entry)
		}
	}

	if len(found) == 1 {
		// on a case sensitive filesystem we cannot determine here, whether the file exists or not, hence we need the 'file exists' precondition
		prefix, err := Realcase(dir, token...) // recurse
		if err != nil {
			return "", err
		}
		if prefix != "" {
			return filepath.Join(prefix, found[0]), nil
		}
	} else if len(found) > 1 {
		// must be a case sensitive filesystem
		for i, foundName := range found {
			if foundName == name {
				prefix, err := Realcase(dir, token...) // recurse
				if err != nil {
					return "", err
				}
				if prefix != "" {
					return filepath.Join(prefix, found[i]), nil
				}
			}
		}
	}

	return "", nil
}

// Realpath returns the real path of a file, resolving symbolic links
func Realpath(path string) (string, error) {
	// DO NOT USE filepath.EvalSymlinks here as it internally
	// calls os.Readlink which will result in subst
	// drives to be resolved to their target on Windows
	// https://github.com/microsoft/vscode/issues/118562
	realPath, err := filepath.Abs(path)
	if err != nil {
		// We hit an error calling filepath.Abs(). Since filepath.Abs() is doing some path normalization
		// we now do a similar normalization and then try again if we can access the path with read
		// permissions at least. If that succeeds, we return that path.
		// filepath.Abs() is resolving symlinks and that can fail in certain cases. The workaround is
		// to not resolve links but to simply see if the path is read accessible or not.
		normalizedPath := normalizePath(path)

		if _, err := os.Stat(normalizedPath); err != nil {
			return "", err
		}

		return normalizedPath, nil
	}

	return realPath, nil
}

// RealpathSync returns the real path of a file, resolving symbolic links (sync version)
//
// Deprecated: always prefer async variants over sync!
func RealpathSync(path string) (string, error) {
	return Realpath(path) // In Go, this is already synchronous
}

func normalizePath(path string) string {
	normalized := filepath.Clean(path)
	// Remove trailing separator (equivalent to rtrim(normalize(path), sep))
	if len(normalized) > 1 && (normalized[len(normalized)-1] == '/' || normalized[len(normalized)-1] == '\\') {
		normalized = normalized[:len(normalized)-1]
	}
	return normalized
}

//#endregion

//#region Promise based fs methods

// PromisesType provides low level fs methods as functions similar to
// fs.promises but with notable differences, either implemented
// by us or by restoring the original callback based behavior.
//
// At least realpath is implemented differently in the promise
// based implementation compared to the callback based one. The
// promise based implementation actually calls fs.realpath.native.
// (https://github.com/microsoft/vscode/issues/118562)
type PromisesType struct{}

// Exists checks if a path exists
func (p *PromisesType) Exists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// ReadDir reads directory entries
func (p *PromisesType) ReadDir(path string) ([]string, error) {
	return Readdir(path)
}

// ReadDirsInDir reads all subdirectories in a directory
func (p *PromisesType) ReadDirsInDir(dirPath string) ([]string, error) {
	return ReadDirsInDir(dirPath)
}

// WriteFile writes data to a file
func (p *PromisesType) WriteFile(path string, data []byte, options *IWriteFileOptions) error {
	return WriteFile(path, data, options)
}

// Rm removes files/directories recursively
func (p *PromisesType) Rm(path string, mode RimRafMode, moveToPath ...string) error {
	return Rimraf(path, mode, moveToPath...)
}

// Rename renames/moves files
func (p *PromisesType) Rename(source, target string, windowsRetryTimeout ...int) error {
	return Rename(source, target, windowsRetryTimeout...)
}

// Copy copies files/directories
func (p *PromisesType) Copy(source, target string, options *CopyOptions) error {
	return Copy(source, target, options)
}

// Realpath returns the real path (fs.promises.realpath will use fs.realpath.native which we do not want)
func (p *PromisesType) Realpath(path string) (string, error) {
	return Realpath(path)
}

// Read reads from a file descriptor
func (p *PromisesType) Read(fd int, buffer []byte, offset, length int, position *int64) (int, []byte, error) {
	file := os.NewFile(uintptr(fd), "")
	if file == nil {
		return 0, nil, fmt.Errorf("invalid file descriptor")
	}

	if position != nil {
		if _, err := file.Seek(*position, 0); err != nil {
			return 0, nil, err
		}
	}

	readBuffer := buffer[offset : offset+length]
	n, err := file.Read(readBuffer)
	return n, readBuffer[:n], err
}

// Write writes to a file descriptor
func (p *PromisesType) Write(fd int, buffer []byte, offset, length *int, position *int64) (int, []byte, error) {
	file := os.NewFile(uintptr(fd), "")
	if file == nil {
		return 0, nil, fmt.Errorf("invalid file descriptor")
	}

	if position != nil {
		if _, err := file.Seek(*position, 0); err != nil {
			return 0, nil, err
		}
	}

	writeOffset := 0
	writeLength := len(buffer)
	if offset != nil {
		writeOffset = *offset
	}
	if length != nil {
		writeLength = *length
	}

	writeBuffer := buffer[writeOffset : writeOffset+writeLength]
	n, err := file.Write(writeBuffer)
	return n, writeBuffer[:n], err
}

// Fdatasync syncs file data to disk
func (p *PromisesType) Fdatasync(fd int) error {
	file := os.NewFile(uintptr(fd), "")
	if file == nil {
		return fmt.Errorf("invalid file descriptor")
	}
	return file.Sync()
}

// Open opens a file
func (p *PromisesType) Open(path string, flag int, mode os.FileMode) (*os.File, error) {
	return os.OpenFile(path, flag, mode)
}

// Close closes a file
func (p *PromisesType) Close(file *os.File) error {
	return file.Close()
}

// Ftruncate truncates a file
func (p *PromisesType) Ftruncate(file *os.File, size int64) error {
	return file.Truncate(size)
}

// Promises is the global instance of PromisesType
var Promises = &PromisesType{}

//#endregion
