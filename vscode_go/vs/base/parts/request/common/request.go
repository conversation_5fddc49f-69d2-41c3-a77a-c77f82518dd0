/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"errors"
	"net/http"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

const offlineName = "Offline"

// OfflineError represents an offline error
type OfflineError struct {
	message string
}

func (e *OfflineError) Error() string {
	return e.message
}

// NewOfflineError creates a new offline error
func NewOfflineError() *OfflineError {
	return &OfflineError{message: offlineName}
}

// IsOfflineError checks if the given error is offline error
func IsOfflineError(err error) bool {
	var offlineErr *OfflineError
	if errors.As(err, &offlineErr) {
		return true
	}
	return err != nil && err.Error() == offlineName
}

// IHeaders represents HTTP headers
type IHeaders map[string]interface{}

// IRequestOptions represents options for HTTP requests
type IRequestOptions struct {
	Type               string   `json:"type,omitempty"`
	URL                string   `json:"url,omitempty"`
	User               string   `json:"user,omitempty"`
	Password           string   `json:"password,omitempty"`
	Headers            IHeaders `json:"headers,omitempty"`
	Timeout            int      `json:"timeout,omitempty"`
	Data               string   `json:"data,omitempty"`
	FollowRedirects    int      `json:"followRedirects,omitempty"`
	ProxyAuthorization string   `json:"proxyAuthorization,omitempty"`
	DisableCache       bool     `json:"disableCache,omitempty"`
}

// IRequestContext represents the context of an HTTP request
type IRequestContext struct {
	Res struct {
		Headers    IHeaders `json:"headers"`
		StatusCode int      `json:"statusCode,omitempty"`
	} `json:"res"`
	Stream baseCommon.VSBufferReadableStream `json:"stream"`
}

// IRawRequestFunction represents a raw request function
type IRawRequestFunction func(options *http.Request) (*http.Response, error)
