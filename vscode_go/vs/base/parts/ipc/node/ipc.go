/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"context"
	"crypto/sha256"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	ipccommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/ipc/common"
)

// XDG_RUNTIME_DIR environment variable for Unix systems
var XDG_RUNTIME_DIR = os.Getenv("XDG_RUNTIME_DIR")

// validateIPCHandleLength validates the length of an IPC handle
func validateIPCHandleLength(handle string) error {
	// On Unix systems, socket paths have a maximum length
	if runtime.GOOS != "windows" {
		const maxSocketPathLength = 108 // Typical Unix socket path limit
		if len(handle) > maxSocketPathLength {
			return fmt.Errorf("IPC handle path too long: %d characters (max %d)", len(handle), maxSocketPathLength)
		}
	}
	return nil
}

// CreateStaticIPCHandle creates a static IPC handle for inter-process communication
func CreateStaticIPCHandle(directoryPath, ipcType, version string) string {
	// Create a hash scope from the directory path
	hash := sha256.Sum256([]byte(directoryPath))
	scope := fmt.Sprintf("%x", hash)
	scopeForSocket := scope[:8] // Take first 8 characters

	// Windows: use named pipe
	if runtime.GOOS == "windows" {
		return fmt.Sprintf(`\\.\pipe\%s-%s-%s-sock`, scopeForSocket, version, ipcType)
	}

	// Mac & Unix: Use socket file
	// Unix: Prefer XDG_RUNTIME_DIR over user data path, unless portable
	// Trim the version and type values for the socket to prevent too large
	// file names causing issues

	versionForSocket := version
	if len(version) > 4 {
		versionForSocket = version[:4]
	}

	typeForSocket := ipcType
	if len(ipcType) > 6 {
		typeForSocket = ipcType[:6]
	}

	var result string
	if runtime.GOOS != "darwin" && XDG_RUNTIME_DIR != "" && os.Getenv("VSCODE_PORTABLE") == "" {
		result = filepath.Join(XDG_RUNTIME_DIR, fmt.Sprintf("vscode-%s-%s-%s.sock", scopeForSocket, versionForSocket, typeForSocket))
	} else {
		result = filepath.Join(directoryPath, fmt.Sprintf("%s-%s.sock", versionForSocket, typeForSocket))
	}

	// Validate length
	if err := validateIPCHandleLength(result); err != nil {
		// If the path is too long, fall back to a shorter version
		result = filepath.Join(directoryPath, fmt.Sprintf("%s.sock", scopeForSocket[:6]))
	}

	return result
}

// CreateRandomIPCHandle creates a random IPC handle
func CreateRandomIPCHandle() string {
	// Generate a random suffix (simplified version)
	randomSuffix := basecommon.GenerateUuid()

	// Windows: use named pipe
	if runtime.GOOS == "windows" {
		return fmt.Sprintf(`\\.\pipe\vscode-ipc-%s-sock`, randomSuffix)
	}

	// Mac & Unix: Use socket file
	// Unix: Prefer XDG_RUNTIME_DIR over user data path
	var basePath string
	if runtime.GOOS != "darwin" && XDG_RUNTIME_DIR != "" {
		basePath = XDG_RUNTIME_DIR
	} else {
		basePath = os.TempDir()
	}

	result := filepath.Join(basePath, fmt.Sprintf("vscode-ipc-%s.sock", randomSuffix))

	// Validate length
	if err := validateIPCHandleLength(result); err != nil {
		// If the path is too long, use a shorter version
		shortSuffix := randomSuffix[:8]
		result = filepath.Join(basePath, fmt.Sprintf("vscode-%s.sock", shortSuffix))
	}

	return result
}

// IPCHandle represents an IPC handle
type IPCHandle struct {
	Path string
	Type string
}

// NewIPCHandle creates a new IPC handle
func NewIPCHandle(path, ipcType string) *IPCHandle {
	return &IPCHandle{
		Path: path,
		Type: ipcType,
	}
}

// IsValid checks if the IPC handle is valid
func (h *IPCHandle) IsValid() bool {
	return h.Path != "" && validateIPCHandleLength(h.Path) == nil
}

// String returns the string representation of the IPC handle
func (h *IPCHandle) String() string {
	return h.Path
}

// IPCServer represents an IPC server interface
type IPCServer interface {
	Listen(handle string) error
	Close() error
	GetHandle() string
}

// IPCClient represents an IPC client interface
type IPCClient interface {
	Connect(handle string) error
	Disconnect() error
	Send(data []byte) error
	Receive() ([]byte, error)
}

// NodeIPCServer represents an IPC server for Node.js-style communication
// Equivalent to Server as NodeIPCServer from ipc.net.js
type NodeIPCServer struct {
	listener net.Listener
	ctx      context.Context
	cancel   context.CancelFunc
	mutex    sync.RWMutex
	channels map[string]ipccommon.IServerChannel[interface{}]
}

// NewNodeIPCServer creates a new NodeIPCServer
func NewNodeIPCServer() *NodeIPCServer {
	ctx, cancel := context.WithCancel(context.Background())
	return &NodeIPCServer{
		ctx:      ctx,
		cancel:   cancel,
		channels: make(map[string]ipccommon.IServerChannel[interface{}]),
	}
}

// Serve starts the IPC server on the given handle
// Equivalent to serve as nodeIPCServe from ipc.net.js
func Serve(handle string) (*NodeIPCServer, error) {
	server := NewNodeIPCServer()

	// Validate handle length
	if err := validateIPCHandleLength(handle); err != nil {
		return nil, err
	}

	var listener net.Listener
	var err error

	if runtime.GOOS == "windows" {
		// Windows named pipe
		listener, err = net.Listen("tcp", handle)
	} else {
		// Unix socket
		// Remove existing socket file if it exists
		os.Remove(handle)
		listener, err = net.Listen("unix", handle)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create IPC server: %w", err)
	}

	server.listener = listener

	// Start accepting connections
	go server.acceptConnections()

	return server, nil
}

// acceptConnections handles incoming connections
func (s *NodeIPCServer) acceptConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.listener.Accept()
			if err != nil {
				continue
			}
			go s.handleConnection(conn)
		}
	}
}

// handleConnection handles a single connection
func (s *NodeIPCServer) handleConnection(conn net.Conn) {
	defer conn.Close()
	// Implementation would handle IPC protocol here
	// For now, this is a placeholder
}

// RegisterChannel registers a channel with the server
func (s *NodeIPCServer) RegisterChannel(name string, channel ipccommon.IServerChannel[interface{}]) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.channels[name] = channel
}

// Dispose closes the server
func (s *NodeIPCServer) Dispose() error {
	s.cancel()
	if s.listener != nil {
		return s.listener.Close()
	}
	return nil
}

// NodeIPCClient represents an IPC client for Node.js-style communication
// Equivalent to Client as NodeIPCClient from ipc.net.js
type NodeIPCClient[T any] struct {
	conn   net.Conn
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex
}

// NewNodeIPCClient creates a new NodeIPCClient
func NewNodeIPCClient[T any]() *NodeIPCClient[T] {
	ctx, cancel := context.WithCancel(context.Background())
	return &NodeIPCClient[T]{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Connect connects to an IPC server
// Equivalent to connect as nodeIPCConnect from ipc.net.js
func Connect[T any](handle, clientName string) (*NodeIPCClient[T], error) {
	client := NewNodeIPCClient[T]()

	var conn net.Conn
	var err error

	if runtime.GOOS == "windows" {
		// Windows named pipe
		conn, err = net.Dial("tcp", handle)
	} else {
		// Unix socket
		conn, err = net.Dial("unix", handle)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to IPC server: %w", err)
	}

	client.conn = conn
	return client, nil
}

// GetChannel gets a channel from the client
func (c *NodeIPCClient[T]) GetChannel(name string) ipccommon.IChannel {
	// Implementation would return a channel proxy
	// For now, this is a placeholder
	return nil
}

// Dispose closes the client connection
func (c *NodeIPCClient[T]) Dispose() error {
	c.cancel()
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
