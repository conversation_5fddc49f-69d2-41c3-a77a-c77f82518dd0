/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"
	"fmt"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	policycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/policy/common"
)

// PolicyUpdate represents a policy update from the watcher
type PolicyUpdate map[string]interface{}

// Watcher represents a policy watcher interface
type Watcher interface {
	basecommon.IDisposable
}

// mockWatcher is a simple mock implementation of Watcher
type mockWatcher struct {
	disposed bool
}

func (mw *mockWatcher) Dispose() {
	mw.disposed = true
}

// NativePolicyService extends AbstractPolicyService and implements IPolicyService
type NativePolicyService struct {
	*policycommon.AbstractPolicyService

	throttler   *basecommon.Throttler
	watcher     *basecommon.MutableDisposable
	logService  logcommon.ILogService
	productName string
	mu          sync.Mutex
}

// NewNativePolicyService creates a new NativePolicyService
func NewNativePolicyService(logService logcommon.ILogService, productName string) *NativePolicyService {
	abstractService := policycommon.NewAbstractPolicyService()

	nps := &NativePolicyService{
		AbstractPolicyService: abstractService,
		throttler:             basecommon.NewThrottler(),
		watcher:               basecommon.NewMutableDisposable(),
		logService:            logService,
		productName:           productName,
	}

	// Register disposables
	abstractService.Register(nps.throttler)
	abstractService.Register(nps.watcher)

	return nps
}

// updatePolicyDefinitions implements the abstract method from AbstractPolicyService
func (nps *NativePolicyService) updatePolicyDefinitions(policyDefinitions map[string]*policycommon.PolicyDefinition) error {
	nps.logService.Trace(fmt.Sprintf("NativePolicyService#_updatePolicyDefinitions - Found %d policy definitions", len(policyDefinitions)))

	// In a real implementation, this would create a policy watcher
	// For now, we'll create a mock watcher
	return nps.throttler.Queue(func() *basecommon.Future[any] {
		completer := basecommon.NewCompleter[any]()

		go func() {
			defer func() {
				if r := recover(); r != nil {
					if err, ok := r.(error); ok {
						nps.logService.Error("NativePolicyService#_updatePolicyDefinitions - Error creating watcher:", err)
						completer.Reject(err)
					} else {
						completer.Reject(fmt.Errorf("panic occurred: %v", r))
					}
				}
			}()

			// Create mock watcher
			watcher := &mockWatcher{}
			nps.watcher.SetValue(watcher)

			// Simulate policy change callback
			nps.onDidPolicyChange(PolicyUpdate{
				"example.policy": "example.value",
			})

			completer.Resolve(nil)
		}()

		return completer.Future
	}).Err()
}

// onDidPolicyChange handles policy changes from the watcher
func (nps *NativePolicyService) onDidPolicyChange(update PolicyUpdate) {
	nps.mu.Lock()
	defer nps.mu.Unlock()

	updateJSON, _ := json.Marshal(update)
	nps.logService.Trace(fmt.Sprintf("NativePolicyService#_onDidPolicyChange - Updated policy values: %s", string(updateJSON)))

	changedPolicies := make([]string, 0, len(update))

	for key, value := range update {
		changedPolicies = append(changedPolicies, key)

		if value == nil {
			nps.SetPolicyValue(key, nil)
		} else {
			nps.SetPolicyValue(key, value)
		}
	}

	nps.FireOnDidChange(changedPolicies)
}

// ServiceBrand implements IPolicyService
func (nps *NativePolicyService) ServiceBrand() interface{} {
	return nil
}
