/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// IDebugParams represents debug parameters
type IDebugParams struct {
	Port  *int `json:"port"`
	Break bool `json:"break"`
}

// IExtensionHostDebugParams extends IDebugParams for extension host debugging
type IExtensionHostDebugParams struct {
	IDebugParams
	DebugID *string           `json:"debugId,omitempty"`
	Env     map[string]string `json:"env,omitempty"`
}

// ExtensionKind represents the type of extension
type ExtensionKind string

const (
	ExtensionKindUI        ExtensionKind = "ui"
	ExtensionKindWorkspace ExtensionKind = "workspace"
	ExtensionKindWeb       ExtensionKind = "web"
)

// IEnvironmentService represents a basic environment service interface
type IEnvironmentService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	//
	// NOTE: KEEP THIS INTERFACE AS SMALL AS POSSIBLE.
	//
	// AS SUCH:
	//   - PUT NON-WEB PROPERTIES INTO NATIVE ENVIRONMENT SERVICE
	//   - PUT WORKBENCH ONLY PROPERTIES INTO WORKBENCH ENVIRONMENT SERVICE
	//   - PUT ELECTRON-MAIN ONLY PROPERTIES INTO MAIN ENVIRONMENT SERVICE
	//
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

	// --- user roaming data
	UserRoamingDataHome() *basecommon.URI
	UserDataSyncHome() *basecommon.URI
	StateResource() *basecommon.URI

	// --- data paths
	UserDataPath() string
	MachineSettingsResource() *basecommon.URI

	// --- logging
	LogsHome() *basecommon.URI

	// --- extensions
	ExtensionsPath() string
	ExtensionsDownloadLocation() *basecommon.URI
	BuiltinExtensionsPath() string

	// --- settings sync
	UserDataSyncLogResource() *basecommon.URI
	SyncExtensionsResource() *basecommon.URI
	SyncGlobalStateResource() *basecommon.URI
	SyncKeybindingsResource() *basecommon.URI
	SyncSettingsResource() *basecommon.URI
	SyncSnippetsResource() *basecommon.URI
	SyncTasksResource() *basecommon.URI

	// --- telemetry
	TelemetryLogResource() *basecommon.URI

	// --- policy
	PolicyFile() *basecommon.URI

	// --- keyboardLayout
	KeyboardLayoutResource() *basecommon.URI

	// --- argvResource
	ArgvResource() *basecommon.URI

	// --- untitled workspace
	UntitledWorkspacesHome() *basecommon.URI

	// --- workspace storage
	WorkspaceStorageHome() *basecommon.URI

	// --- development
	IsExtensionDevelopment() bool
	DisableExtensions() bool
	DisableDefaultExtensions() bool
	Verbose() bool
	LogLevel() *string
	ExtensionDevelopmentLocationURI() []*basecommon.URI
	ExtensionDevelopmentKind() []ExtensionKind
	ExtensionTestsLocationURI() *basecommon.URI

	// --- remote
	RemoteAuthority() *string
	ExpectsResolverExtension() bool

	// --- driver
	DriverHandle() *string
	DriverVerbose() bool

	// --- web
	WebviewExternalEndpoint() string
	WebviewResourceRoot() string
	WebviewCspSource() string

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	//
	// NOTE: KEEP THIS INTERFACE AS SMALL AS POSSIBLE.
	//
	// AS SUCH:
	//   - PUT NON-WEB PROPERTIES INTO NATIVE ENVIRONMENT SERVICE
	//   - PUT WORKBENCH ONLY PROPERTIES INTO WORKBENCH ENVIRONMENT SERVICE
	//   - PUT ELECTRON-MAIN ONLY PROPERTIES INTO MAIN ENVIRONMENT SERVICE
	//
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
}

// INativeEnvironmentService represents a native environment service interface
type INativeEnvironmentService interface {
	IEnvironmentService

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	//
	// NOTE: KEEP THIS INTERFACE AS SMALL AS POSSIBLE.
	//
	// AS SUCH:
	//   - PUT WORKBENCH ONLY PROPERTIES INTO WORKBENCH ENVIRONMENT SERVICE
	//   - PUT ELECTRON-MAIN ONLY PROPERTIES INTO MAIN ENVIRONMENT SERVICE
	//
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

	// --- CLI Arguments
	Args() NativeParsedArgs

	// --- data paths
	// Root path of the JavaScript sources.
	//
	// Note: This is NOT the installation root
	// directory itself but contained in it at
	// a level that is platform dependent.
	AppRoot() string
	UserHome() *basecommon.URI
	AppSettingsHome() *basecommon.URI
	TmpDir() *basecommon.URI

	// --- use in-memory Secret Storage
	UseInMemorySecretStorage() *bool

	CrossOriginIsolated() *bool

	// --- build info
	IsBuilt() bool

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	//
	// NOTE: KEEP THIS INTERFACE AS SMALL AS POSSIBLE.
	//
	// AS SUCH:
	//   - PUT NON-WEB PROPERTIES INTO NATIVE ENVIRONMENT SERVICE
	//   - PUT WORKBENCH ONLY PROPERTIES INTO WORKBENCH ENVIRONMENT SERVICE
	//   - PUT ELECTRON-MAIN ONLY PROPERTIES INTO MAIN ENVIRONMENT SERVICE
	//
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
}

// Service identifiers for dependency injection
type EnvironmentServiceIdentifier struct{}

func (e *EnvironmentServiceIdentifier) GetServiceID() string {
	return "environmentService"
}

type NativeEnvironmentServiceIdentifier struct{}

func (n *NativeEnvironmentServiceIdentifier) GetServiceID() string {
	return "nativeEnvironmentService"
}

// Global service identifier instances
var (
	IEnvironmentServiceID       = &EnvironmentServiceIdentifier{}
	INativeEnvironmentServiceID = &NativeEnvironmentServiceIdentifier{}
)
