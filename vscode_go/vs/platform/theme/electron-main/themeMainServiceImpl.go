/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"strings"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformConfiguration "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	platformLog "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	platformState "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	platformTheme "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/theme/common"
	platformWindow "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/common"
	platformWindows "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/windows/electron-main"
	platformWorkspace "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// These default colors match our default themes
// editor background color ("Dark Modern", etc...)
const (
	DEFAULT_BG_LIGHT    = "#FFFFFF"
	DEFAULT_BG_DARK     = "#1F1F1F"
	DEFAULT_BG_HC_BLACK = "#000000"
	DEFAULT_BG_HC_LIGHT = "#FFFFFF"
)

const (
	THEME_STORAGE_KEY                = "theme"
	THEME_BG_STORAGE_KEY             = "themeBackground"
	THEME_WINDOW_SPLASH_KEY          = "windowSplash"
	THEME_WINDOW_SPLASH_OVERRIDE_KEY = "windowSplashWorkspaceOverride"
	AUXILIARYBAR_DEFAULT_VISIBILITY  = "workbench.secondarySideBar.defaultVisibility"
)

// ThemeSettings constants
const (
	DETECT_COLOR_SCHEME = "window.autoDetectColorScheme"
	DETECT_HC           = "window.autoDetectHighContrast"
	SYSTEM_COLOR_THEME  = "window.systemColorTheme"
)

// IPartSplashOverrideWorkspaces represents workspace-specific splash overrides
type IPartSplashOverrideWorkspaces map[string]struct {
	SideBarVisible      bool `json:"sideBarVisible"`
	AuxiliaryBarVisible bool `json:"auxiliaryBarVisible"`
}

// IPartsSplashOverride represents splash screen override configuration
type IPartsSplashOverride struct {
	LayoutInfo struct {
		SideBarWidth      int                           `json:"sideBarWidth"`
		AuxiliaryBarWidth int                           `json:"auxiliaryBarWidth"`
		Workspaces        IPartSplashOverrideWorkspaces `json:"workspaces"`
	} `json:"layoutInfo"`
}

// ThemeMainServiceImpl extends Disposable and implements IThemeMainService
type ThemeMainServiceImpl struct {
	baseCommon.Disposable

	// Static constants
	DEFAULT_BAR_WIDTH        int
	WORKSPACE_OVERRIDE_LIMIT int

	// Events
	onDidChangeColorScheme *baseCommon.Emitter[platformWindow.IColorScheme]

	// Services
	stateService         platformState.IStateService
	configurationService platformConfiguration.IConfigurationService
	logService           platformLog.ILogService
}

// NewThemeMainServiceImpl creates a new ThemeMainServiceImpl
func NewThemeMainServiceImpl(
	stateService platformState.IStateService,
	configurationService platformConfiguration.IConfigurationService,
	logService platformLog.ILogService,
) *ThemeMainServiceImpl {
	service := &ThemeMainServiceImpl{
		DEFAULT_BAR_WIDTH:        300,
		WORKSPACE_OVERRIDE_LIMIT: 50,
		onDidChangeColorScheme:   baseCommon.NewEmitter[platformWindow.IColorScheme](),
		stateService:             stateService,
		configurationService:     configurationService,
		logService:               logService,
	}

	// System Theme - skip Linux check for now in Wails v3
	// For now, we'll simplify the configuration change handling
	// In a full implementation, we would properly handle configuration changes

	service.updateSystemColorTheme()

	return service
}

// ServiceBrand implements the service brand
func (s *ThemeMainServiceImpl) ServiceBrand() interface{} {
	return "themeMainService"
}

func (s *ThemeMainServiceImpl) updateSystemColorTheme() {
	// In Wails v3, we would use native APIs to detect system theme
	// For now, we'll use configuration-based detection

	if s.configurationService.GetValue(DETECT_COLOR_SCHEME, false).(bool) {
		// Use system theme detection
		return
	}

	systemColorTheme := s.configurationService.GetValue(SYSTEM_COLOR_THEME, "default").(string)
	switch systemColorTheme {
	case "dark":
		// Set dark theme
	case "light":
		// Set light theme
	case "auto":
		preferred := s.GetPreferredBaseTheme()
		stored := s.getStoredBaseTheme()
		theme := preferred
		if theme == "" {
			theme = stored
		}
		switch theme {
		case string(platformTheme.VS):
			// Set light theme
		case string(platformTheme.VSDark):
			// Set dark theme
		default:
			// Use system theme
		}
	default:
		// Use system theme
	}
}

// GetColorScheme returns the current color scheme
func (s *ThemeMainServiceImpl) GetColorScheme() platformWindow.IColorScheme {
	// In Wails v3, we would use native APIs to detect the actual system color scheme
	// For now, return a default scheme
	return platformWindow.IColorScheme{
		Dark:         false,
		HighContrast: false,
	}
}

// GetPreferredBaseTheme returns the preferred base theme based on system settings
func (s *ThemeMainServiceImpl) GetPreferredBaseTheme() string {
	colorScheme := s.GetColorScheme()

	if s.configurationService.GetValue(DETECT_HC, false).(bool) && colorScheme.HighContrast {
		if colorScheme.Dark {
			return string(platformTheme.HCBlack)
		}
		return string(platformTheme.HCLight)
	}

	if s.configurationService.GetValue(DETECT_COLOR_SCHEME, false).(bool) {
		if colorScheme.Dark {
			return string(platformTheme.VSDark)
		}
		return string(platformTheme.VS)
	}

	return ""
}

// GetBackgroundColor returns the background color for the current theme
func (s *ThemeMainServiceImpl) GetBackgroundColor() string {
	preferred := s.GetPreferredBaseTheme()
	stored := s.getStoredBaseTheme()

	// If the stored theme has the same base as the preferred, we can return the stored background
	if preferred == "" || preferred == stored {
		storedBackground := s.stateService.GetItem(THEME_BG_STORAGE_KEY, nil)
		if storedBackground != nil {
			if bg, ok := storedBackground.(string); ok && bg != "" {
				return bg
			}
		}
	}

	// Otherwise we return the default background for the preferred base theme
	theme := preferred
	if theme == "" {
		theme = stored
	}

	switch theme {
	case string(platformTheme.VS):
		return DEFAULT_BG_LIGHT
	case string(platformTheme.HCBlack):
		return DEFAULT_BG_HC_BLACK
	case string(platformTheme.HCLight):
		return DEFAULT_BG_HC_LIGHT
	default:
		return DEFAULT_BG_DARK
	}
}

func (s *ThemeMainServiceImpl) getStoredBaseTheme() string {
	storedTheme := s.stateService.GetItem(THEME_STORAGE_KEY, string(platformTheme.VSDark))
	if themeStr, ok := storedTheme.(string); ok {
		baseTheme := strings.Split(themeStr, " ")[0]
		switch baseTheme {
		case string(platformTheme.VS):
			return string(platformTheme.VS)
		case string(platformTheme.HCBlack):
			return string(platformTheme.HCBlack)
		case string(platformTheme.HCLight):
			return string(platformTheme.HCLight)
		default:
			return string(platformTheme.VSDark)
		}
	}
	return string(platformTheme.VSDark)
}

// OnDidChangeColorScheme returns the color scheme change event
func (s *ThemeMainServiceImpl) OnDidChangeColorScheme() baseCommon.Event[platformWindow.IColorScheme] {
	return s.onDidChangeColorScheme.Event()
}

// SaveWindowSplash saves the window splash configuration
func (s *ThemeMainServiceImpl) SaveWindowSplash(windowID *int, workspace interface{}, splash platformTheme.IPartsSplash) {
	// Update override as needed
	splashOverride := s.updateWindowSplashOverride(workspace, splash)

	// Prepare items to store
	items := []platformState.StateItem{
		{Key: THEME_STORAGE_KEY, Data: splash.BaseTheme},
		{Key: THEME_BG_STORAGE_KEY, Data: splash.ColorInfo.Background},
		{Key: THEME_WINDOW_SPLASH_KEY, Data: splash},
	}

	if splashOverride != nil {
		items = append(items, platformState.StateItem{
			Key:  THEME_WINDOW_SPLASH_OVERRIDE_KEY,
			Data: splashOverride,
		})
	}

	// Update in storage
	s.stateService.SetItems(items)

	// Update in opened windows
	if windowID != nil {
		s.updateBackgroundColor(*windowID, splash)
	}

	// Update system theme
	s.updateSystemColorTheme()
}

func (s *ThemeMainServiceImpl) updateWindowSplashOverride(workspace interface{}, splash platformTheme.IPartsSplash) *IPartsSplashOverride {
	var splashOverride *IPartsSplashOverride
	changed := false

	if workspace != nil {
		// Make a copy for modifications
		existing := s.getWindowSplashOverride()
		splashOverride = &IPartsSplashOverride{
			LayoutInfo: struct {
				SideBarWidth      int                           `json:"sideBarWidth"`
				AuxiliaryBarWidth int                           `json:"auxiliaryBarWidth"`
				Workspaces        IPartSplashOverrideWorkspaces `json:"workspaces"`
			}{
				SideBarWidth:      existing.LayoutInfo.SideBarWidth,
				AuxiliaryBarWidth: existing.LayoutInfo.AuxiliaryBarWidth,
				Workspaces:        make(IPartSplashOverrideWorkspaces),
			},
		}

		// Copy workspaces
		for k, v := range existing.LayoutInfo.Workspaces {
			splashOverride.LayoutInfo.Workspaces[k] = v
		}

		changed = s.doUpdateWindowSplashOverride(workspace, splash, splashOverride, "sideBar")
		changed = s.doUpdateWindowSplashOverride(workspace, splash, splashOverride, "auxiliaryBar") || changed
	}

	if changed {
		return splashOverride
	}
	return nil
}

func (s *ThemeMainServiceImpl) updateBackgroundColor(windowID int, splash platformTheme.IPartsSplash) {
	// In Wails v3, we would update the window background color
	// For now, we'll use the windows service to get all windows and update them
	windows := platformWindows.GetAllWindowsExcludingOffscreen()
	for _, window := range windows {
		// In the original TypeScript, this would be:
		// if (window.id === windowID) {
		//     window.setBackgroundColor(splash.colorInfo.background);
		//     break;
		// }
		// For now, we'll just log or handle this differently in Wails v3
		_ = window // Use the window interface
	}
}

// GetWindowSplash gets the window splash configuration
func (s *ThemeMainServiceImpl) GetWindowSplash(workspace interface{}) *platformTheme.IPartsSplash {
	defer func() {
		if r := recover(); r != nil {
			s.logService.Error("[theme main service] Failed to get window splash", r)
		}
	}()

	return s.doGetWindowSplash(workspace)
}

func (s *ThemeMainServiceImpl) doGetWindowSplash(workspace interface{}) *platformTheme.IPartsSplash {
	partSplashData := s.stateService.GetItem(THEME_WINDOW_SPLASH_KEY, nil)
	if partSplashData == nil {
		return nil
	}

	// Convert to IPartsSplash
	partSplash, ok := partSplashData.(platformTheme.IPartsSplash)
	if !ok {
		return nil
	}

	if partSplash.LayoutInfo == nil {
		return &partSplash // return early: overrides currently only apply to layout info
	}

	override := s.getWindowSplashOverride()

	// Figure out side bar width based on workspace and overrides
	var sideBarWidth int
	if workspace != nil {
		workspaceID := s.getWorkspaceID(workspace)
		if workspaceOverride, exists := override.LayoutInfo.Workspaces[workspaceID]; exists && !workspaceOverride.SideBarVisible {
			sideBarWidth = 0
		} else {
			sideBarWidth = override.LayoutInfo.SideBarWidth
			if sideBarWidth == 0 && partSplash.LayoutInfo != nil {
				sideBarWidth = partSplash.LayoutInfo.SideBarWidth
			}
			if sideBarWidth == 0 {
				sideBarWidth = s.DEFAULT_BAR_WIDTH
			}
		}
	} else {
		sideBarWidth = 0
	}

	// Figure out auxiliary bar width based on workspace, configuration and overrides
	auxiliaryBarDefaultVisibility := s.configurationService.GetValue(AUXILIARYBAR_DEFAULT_VISIBILITY, "").(string)
	var auxiliaryBarWidth int
	if workspace != nil {
		workspaceID := s.getWorkspaceID(workspace)
		workspaceOverride, exists := override.LayoutInfo.Workspaces[workspaceID]

		if exists && workspaceOverride.AuxiliaryBarVisible {
			auxiliaryBarWidth = override.LayoutInfo.AuxiliaryBarWidth
			if auxiliaryBarWidth == 0 && partSplash.LayoutInfo != nil {
				auxiliaryBarWidth = partSplash.LayoutInfo.AuxiliaryBarWidth
			}
			if auxiliaryBarWidth == 0 {
				auxiliaryBarWidth = s.DEFAULT_BAR_WIDTH
			}
		} else if exists && !workspaceOverride.AuxiliaryBarVisible {
			auxiliaryBarWidth = 0
		} else {
			if auxiliaryBarDefaultVisibility == "visible" || auxiliaryBarDefaultVisibility == "visibleInWorkspace" {
				auxiliaryBarWidth = override.LayoutInfo.AuxiliaryBarWidth
				if auxiliaryBarWidth == 0 && partSplash.LayoutInfo != nil {
					auxiliaryBarWidth = partSplash.LayoutInfo.AuxiliaryBarWidth
				}
				if auxiliaryBarWidth == 0 {
					auxiliaryBarWidth = s.DEFAULT_BAR_WIDTH
				}
			} else {
				auxiliaryBarWidth = 0
			}
		}
	} else {
		auxiliaryBarWidth = 0 // technically not true if configured 'visible', but we never store splash per empty window
	}

	// Create new layout info with updated widths
	newLayoutInfo := *partSplash.LayoutInfo
	newLayoutInfo.SideBarWidth = sideBarWidth
	newLayoutInfo.AuxiliaryBarWidth = auxiliaryBarWidth

	result := partSplash
	result.LayoutInfo = &newLayoutInfo

	return &result
}

func (s *ThemeMainServiceImpl) getWindowSplashOverride() *IPartsSplashOverride {
	overrideData := s.stateService.GetItem(THEME_WINDOW_SPLASH_OVERRIDE_KEY, nil)

	var override *IPartsSplashOverride
	if overrideData != nil {
		if existingOverride, ok := overrideData.(*IPartsSplashOverride); ok {
			override = existingOverride
		}
	}

	if override == nil || override.LayoutInfo.Workspaces == nil {
		override = &IPartsSplashOverride{
			LayoutInfo: struct {
				SideBarWidth      int                           `json:"sideBarWidth"`
				AuxiliaryBarWidth int                           `json:"auxiliaryBarWidth"`
				Workspaces        IPartSplashOverrideWorkspaces `json:"workspaces"`
			}{
				SideBarWidth:      s.DEFAULT_BAR_WIDTH,
				AuxiliaryBarWidth: s.DEFAULT_BAR_WIDTH,
				Workspaces:        make(IPartSplashOverrideWorkspaces),
			},
		}
	}

	if override.LayoutInfo.SideBarWidth == 0 {
		override.LayoutInfo.SideBarWidth = s.DEFAULT_BAR_WIDTH
	}

	if override.LayoutInfo.AuxiliaryBarWidth == 0 {
		override.LayoutInfo.AuxiliaryBarWidth = s.DEFAULT_BAR_WIDTH
	}

	if override.LayoutInfo.Workspaces == nil {
		override.LayoutInfo.Workspaces = make(IPartSplashOverrideWorkspaces)
	}

	return override
}

func (s *ThemeMainServiceImpl) getWorkspaceID(workspace interface{}) string {
	if workspace == nil {
		return "empty"
	}

	// Try to extract ID from workspace
	if w, ok := workspace.(*platformWorkspace.IWorkspaceIdentifier); ok {
		return "workspace_" + w.GetID()
	}

	if w, ok := workspace.(*platformWorkspace.ISingleFolderWorkspaceIdentifier); ok {
		return "folder_" + w.GetID()
	}

	// Fallback to string representation
	return "unknown"
}

func (s *ThemeMainServiceImpl) doUpdateWindowSplashOverride(workspace interface{}, splash platformTheme.IPartsSplash, splashOverride *IPartsSplashOverride, part string) bool {
	workspaceID := s.getWorkspaceID(workspace)
	if workspaceID == "" {
		return false
	}

	var currentWidth int
	var overrideWidth int

	if part == "sideBar" {
		if splash.LayoutInfo != nil {
			currentWidth = splash.LayoutInfo.SideBarWidth
		}
		overrideWidth = splashOverride.LayoutInfo.SideBarWidth
	} else {
		if splash.LayoutInfo != nil {
			currentWidth = splash.LayoutInfo.AuxiliaryBarWidth
		}
		overrideWidth = splashOverride.LayoutInfo.AuxiliaryBarWidth
	}

	// No layout info: remove override
	changed := false
	if currentWidth == 0 {
		if _, exists := splashOverride.LayoutInfo.Workspaces[workspaceID]; exists {
			delete(splashOverride.LayoutInfo.Workspaces, workspaceID)
			changed = true
		}
		return changed
	}

	workspaceOverride, exists := splashOverride.LayoutInfo.Workspaces[workspaceID]
	if !exists {
		// Check workspace limit
		if len(splashOverride.LayoutInfo.Workspaces) >= s.WORKSPACE_OVERRIDE_LIMIT {
			// Remove first entry (oldest)
			for k := range splashOverride.LayoutInfo.Workspaces {
				delete(splashOverride.LayoutInfo.Workspaces, k)
				changed = true
				break
			}
		}

		workspaceOverride = struct {
			SideBarVisible      bool `json:"sideBarVisible"`
			AuxiliaryBarVisible bool `json:"auxiliaryBarVisible"`
		}{
			SideBarVisible:      false,
			AuxiliaryBarVisible: false,
		}
		splashOverride.LayoutInfo.Workspaces[workspaceID] = workspaceOverride
		changed = true
	}

	// Part has width: update width & visibility override
	if currentWidth > 0 {
		if overrideWidth != currentWidth {
			if part == "sideBar" {
				splashOverride.LayoutInfo.SideBarWidth = currentWidth
			} else {
				splashOverride.LayoutInfo.AuxiliaryBarWidth = currentWidth
			}
			changed = true
		}

		if part == "sideBar" {
			if !workspaceOverride.SideBarVisible {
				workspaceOverride.SideBarVisible = true
				splashOverride.LayoutInfo.Workspaces[workspaceID] = workspaceOverride
				changed = true
			}
		} else {
			if !workspaceOverride.AuxiliaryBarVisible {
				workspaceOverride.AuxiliaryBarVisible = true
				splashOverride.LayoutInfo.Workspaces[workspaceID] = workspaceOverride
				changed = true
			}
		}
	} else {
		// Part is hidden: update visibility override
		if part == "sideBar" {
			if workspaceOverride.SideBarVisible {
				workspaceOverride.SideBarVisible = false
				splashOverride.LayoutInfo.Workspaces[workspaceID] = workspaceOverride
				changed = true
			}
		} else {
			if workspaceOverride.AuxiliaryBarVisible {
				workspaceOverride.AuxiliaryBarVisible = false
				splashOverride.LayoutInfo.Workspaces[workspaceID] = workspaceOverride
				changed = true
			}
		}
	}

	return changed
}
