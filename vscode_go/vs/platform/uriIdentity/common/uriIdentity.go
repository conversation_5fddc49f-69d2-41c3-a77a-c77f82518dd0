/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	files "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	instantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IUriIdentityService provides URI identity and comparison services
type IUriIdentityService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// ExtUri provides URI extensions that are aware of casing
	ExtUri() basecommon.IExtUri

	// AsCanonicalUri returns a canonical uri for the given resource
	AsCanonicalUri(uri *basecommon.URI) *basecommon.URI
}

// Entry represents a cache entry with timestamp
type Entry struct {
	time int
	uri  *basecommon.URI
}

var entryClock = 0

// NewEntry creates a new cache entry
func NewEntry(uri *basecommon.URI) *Entry {
	entryClock++
	return &Entry{
		time: entryClock,
		uri:  uri,
	}
}

// Touch updates the entry's timestamp
func (e *Entry) Touch() *Entry {
	entryClock++
	e.time = entryClock
	return e
}

// UriIdentityService implements IUriIdentityService
type UriIdentityService struct {
	basecommon.Disposable
	fileService   files.IFileService
	extUri        basecommon.IExtUri
	canonicalUris *basecommon.SkipList[*basecommon.URI, *Entry]
	limit         int
}

// NewUriIdentityService creates a new URI identity service
func NewUriIdentityService(fileService files.IFileService) *UriIdentityService {
	schemeIgnoresPathCasingCache := make(map[string]bool)

	ignorePathCasing := func(uri *basecommon.URI) bool {
		val, ok := schemeIgnoresPathCasingCache[uri.Scheme]
		if !ok {
			val = fileService.HasProvider(uri) && !fileService.HasCapability(uri, files.FileSystemProviderCapabilitiesPathCaseSensitive)
			schemeIgnoresPathCasingCache[uri.Scheme] = val
		}
		return val
	}

	// TODO: Listen for file system provider changes to clear the cache

	extUri := basecommon.NewExtUri(ignorePathCasing)
	limit := 65536 // 2^16

	canonicalUris := basecommon.NewSkipList[*basecommon.URI, *Entry](func(a, b *basecommon.URI) int {
		return extUri.Compare(a, b, true)
	}, limit)

	return &UriIdentityService{
		fileService:   fileService,
		extUri:        extUri,
		canonicalUris: canonicalUris,
		limit:         limit,
	}
}

// ServiceBrand implements the service brand
func (uis *UriIdentityService) ServiceBrand() interface{} {
	return "uriIdentityService"
}

// ExtUri returns the extended URI operations
func (uis *UriIdentityService) ExtUri() basecommon.IExtUri {
	return uis.extUri
}

// AsCanonicalUri returns a canonical URI
func (uis *UriIdentityService) AsCanonicalUri(uri *basecommon.URI) *basecommon.URI {
	// (1) normalize URI
	if uis.fileService.HasProvider(uri) {
		uri = uis.extUri.NormalizePath(uri)
	}

	// (2) find the uri in its canonical form or use this uri to define it
	item, exists := uis.canonicalUris.Get(uri)
	if exists {
		return item.Touch().uri.With(basecommon.UriComponents{Fragment: uri.Fragment})
	}

	// this uri is first and defines the canonical form
	uis.canonicalUris.Set(uri, NewEntry(uri))
	uis.checkTrim()

	return uri
}

func (uis *UriIdentityService) checkTrim() {
	if uis.canonicalUris.Size() < uis.limit {
		return
	}

	// get all entries, sort by time (MRU) and re-initialize
	// the uri cache and the entry clock. this is an expensive
	// operation and should happen rarely
	entries := uis.canonicalUris.Entries()

	// Sort entries by time (most recently used first)
	for i := 0; i < len(entries)-1; i++ {
		for j := i + 1; j < len(entries); j++ {
			entryI := entries[i][1].(*Entry)
			entryJ := entries[j][1].(*Entry)
			if entryI.time < entryJ.time {
				entries[i], entries[j] = entries[j], entries[i]
			}
		}
	}

	entryClock = 0
	uis.canonicalUris.Clear()
	newSize := uis.limit / 2
	for i := 0; i < newSize && i < len(entries); i++ {
		uri := entries[i][0].(*basecommon.URI)
		entry := entries[i][1].(*Entry)
		uis.canonicalUris.Set(uri, entry.Touch())
	}
}

// Dispose disposes the URI identity service
func (uis *UriIdentityService) Dispose() {
	uis.Disposable.Dispose()
	uis.canonicalUris.Clear()
}

// Service identifier
var IUriIdentityServiceID = instantiation.CreateDecorator[IUriIdentityService]("uriIdentityService")
