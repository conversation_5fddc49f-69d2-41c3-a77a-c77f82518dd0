/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basePartsRequest "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/request/common"
	platformConfiguration "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	platformLog "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	platformRequest "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/request/common"
)

// IRawRequestFunction represents a raw request function
type IRawRequestFunction func(options *http.Request) (*http.Response, error)

// NodeRequestOptions extends IRequestOptions with Node.js specific options
type NodeRequestOptions struct {
	*basePartsRequest.IRequestOptions
	Agent             interface{}         `json:"agent,omitempty"`
	StrictSSL         bool                `json:"strictSSL,omitempty"`
	IsChromiumNetwork bool                `json:"isChromiumNetwork,omitempty"`
	GetRawRequest     IRawRequestFunction `json:"getRawRequest,omitempty"`
}

// RequestService implements the Node.js request service
type RequestService struct {
	baseCommon.Disposable
	machine              string
	configurationService platformConfiguration.IConfigurationService
	logService           platformLog.ILogService
	proxyURL             string
	strictSSL            bool
	authorization        string
	shellEnvErrorLogged  bool
	counter              int
}

// NewRequestService creates a new RequestService
func NewRequestService(
	machine string,
	configurationService platformConfiguration.IConfigurationService,
	logService platformLog.ILogService,
) *RequestService {
	service := &RequestService{
		machine:              machine,
		configurationService: configurationService,
		logService:           logService,
		counter:              0,
	}

	service.configure()

	// TODO: Register for configuration changes in a full implementation
	// For now, we'll skip the event registration to avoid complexity

	return service
}

// GetServiceBrand returns the service brand
func (s *RequestService) GetServiceBrand() string {
	return "requestService"
}

func (s *RequestService) configure() {
	s.proxyURL = s.getConfigValue("http.proxy", "").(string)
	s.strictSSL = s.getConfigValue("http.proxyStrictSSL", false).(bool)
	s.authorization = s.getConfigValue("http.proxyAuthorization", "").(string)
}

func (s *RequestService) getConfigValue(key string, defaultValue interface{}) interface{} {
	// Simplified configuration access for now
	return s.configurationService.GetValue(key, defaultValue)
}

// Request makes an HTTP request
func (s *RequestService) Request(options *basePartsRequest.IRequestOptions, token baseCommon.CancellationToken) (*basePartsRequest.IRequestContext, error) {
	nodeOptions := &NodeRequestOptions{
		IRequestOptions: options,
		StrictSSL:       s.strictSSL,
	}

	return s.logAndRequest(options, func() (*basePartsRequest.IRequestContext, error) {
		return s.nodeRequest(nodeOptions, token)
	})
}

func (s *RequestService) logAndRequest(options *basePartsRequest.IRequestOptions, request func() (*basePartsRequest.IRequestContext, error)) (*basePartsRequest.IRequestContext, error) {
	s.counter++
	prefix := fmt.Sprintf("#%d: %s", s.counter, options.URL)

	s.logService.Trace(fmt.Sprintf("%s - begin", prefix), options.Type, s.loggableHeaders(options.Headers))

	result, err := request()
	if err != nil {
		s.logService.Error(fmt.Sprintf("%s - error", prefix), options.Type, err.Error())
		return nil, err
	}

	s.logService.Trace(fmt.Sprintf("%s - end", prefix), options.Type, result.Res.StatusCode, result.Res.Headers)
	return result, nil
}

func (s *RequestService) loggableHeaders(headers basePartsRequest.IHeaders) basePartsRequest.IHeaders {
	if headers == nil {
		return nil
	}

	result := make(basePartsRequest.IHeaders)
	for key, value := range headers {
		lowerKey := strings.ToLower(key)
		if lowerKey == "authorization" || lowerKey == "proxy-authorization" {
			result[key] = "*****"
		} else {
			result[key] = value
		}
	}
	return result
}

func (s *RequestService) nodeRequest(options *NodeRequestOptions, token baseCommon.CancellationToken) (*basePartsRequest.IRequestContext, error) {
	// Create HTTP request
	req, err := http.NewRequest(strings.ToUpper(options.Type), options.URL, strings.NewReader(options.Data))
	if err != nil {
		return nil, err
	}

	// Set headers
	if options.Headers != nil {
		for key, value := range options.Headers {
			if strValue, ok := value.(string); ok {
				req.Header.Set(key, strValue)
			}
		}
	}

	// Add proxy authorization if configured
	if s.authorization != "" {
		req.Header.Set("Proxy-Authorization", s.authorization)
	}

	// Create HTTP client with timeout
	client := &http.Client{}
	if options.Timeout > 0 {
		client.Timeout = time.Duration(options.Timeout) * time.Millisecond
	}

	// Create context with cancellation
	ctx := context.Background()
	if token != nil {
		// For now, we'll use a simple context without cancellation
		// In a full implementation, we would integrate with the cancellation token
		ctx = context.Background()
	}
	req = req.WithContext(ctx)

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	// Convert headers
	headers := make(basePartsRequest.IHeaders)
	for key, values := range resp.Header {
		if len(values) == 1 {
			headers[key] = values[0]
		} else {
			headers[key] = values
		}
	}

	// Create response context
	// For now, we'll create a simple stream implementation
	stream := &baseCommon.SimpleReadableStream{}
	context := &basePartsRequest.IRequestContext{
		Stream: stream,
	}
	context.Res.Headers = headers
	context.Res.StatusCode = resp.StatusCode

	return context, nil
}

// ResolveProxy resolves proxy for a URL
func (s *RequestService) ResolveProxy(url string) (string, error) {
	return "", nil // currently not implemented in node
}

// LookupAuthorization looks up authorization credentials
func (s *RequestService) LookupAuthorization(authInfo *platformRequest.AuthInfo) (*platformRequest.Credentials, error) {
	return nil, nil // currently not implemented in node
}

// LookupKerberosAuthorization looks up Kerberos authorization
func (s *RequestService) LookupKerberosAuthorization(url string) (string, error) {
	return "", nil // currently not implemented in node
}

// LoadCertificates loads certificates
func (s *RequestService) LoadCertificates() ([]string, error) {
	return []string{}, nil // currently not implemented in node
}
