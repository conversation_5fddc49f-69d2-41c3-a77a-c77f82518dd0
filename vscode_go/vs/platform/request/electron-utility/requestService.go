/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronutility

import (
	"net/http"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basePartsRequest "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/request/common"
	platformConfiguration "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	platformLog "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	platformRequestNode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/request/node"
)

// getRawRequest returns a raw request function for Electron net module
func getRawRequest(options *basePartsRequest.IRequestOptions) platformRequestNode.IRawRequestFunction {
	// In Wails v3, we use standard Go HTTP client instead of Electron's net module
	return func(req *http.Request) (*http.Response, error) {
		client := &http.Client{}
		return client.Do(req)
	}
}

// RequestService extends NodeRequestService for Electron utility process
type RequestService struct {
	*platformRequestNode.RequestService
}

// NewRequestService creates a new RequestService for Electron utility process
func NewRequestService(
	configurationService platformConfiguration.IConfigurationService,
	logService platformLog.ILogService,
) *RequestService {
	nodeService := platformRequestNode.NewRequestService("local", configurationService, logService)

	return &RequestService{
		RequestService: nodeService,
	}
}

// Request makes an HTTP request with Electron-specific handling
func (s *RequestService) Request(options *basePartsRequest.IRequestOptions, token baseCommon.CancellationToken) (*basePartsRequest.IRequestContext, error) {
	// Create enhanced options with Electron-specific settings
	enhancedOptions := &basePartsRequest.IRequestOptions{
		Type:               options.Type,
		URL:                options.URL,
		User:               options.User,
		Password:           options.Password,
		Headers:            options.Headers,
		Timeout:            options.Timeout,
		Data:               options.Data,
		FollowRedirects:    options.FollowRedirects,
		ProxyAuthorization: options.ProxyAuthorization,
		DisableCache:       options.DisableCache,
	}

	// In the original TypeScript, this would use Electron's net.request
	// In Wails v3, we use the parent's request method with enhanced options
	return s.RequestService.Request(enhancedOptions, token)
}
