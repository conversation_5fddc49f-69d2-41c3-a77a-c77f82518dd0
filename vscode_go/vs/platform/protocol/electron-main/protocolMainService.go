/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"fmt"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	ipcelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/ipc/electron-main"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// ProtocolCallback represents a callback function for protocol requests
type ProtocolCallback func(result interface{})

// ProtocolRequest represents a protocol request
type ProtocolRequest struct {
	URL string
}

// FilePathWithHeaders represents a file path with headers
type FilePathWithHeaders struct {
	Path    string            `json:"path"`
	Headers map[string]string `json:"headers"`
}

// ErrorResult represents an error result
type ErrorResult struct {
	Error int `json:"error"`
}

// ipcObjectUrl implements IIPCObjectUrl
type ipcObjectUrl[T any] struct {
	resource   *basecommon.URI
	obj        T
	disposed   bool
	mu         sync.Mutex
	logService logcommon.ILogService
}

// Resource implements IIPCObjectUrl
func (iou *ipcObjectUrl[T]) Resource() *basecommon.URI {
	return iou.resource
}

// Update implements IIPCObjectUrl
func (iou *ipcObjectUrl[T]) Update(obj T) {
	iou.mu.Lock()
	defer iou.mu.Unlock()

	if !iou.disposed {
		iou.obj = obj
	}
}

// Dispose implements IIPCObjectUrl
func (iou *ipcObjectUrl[T]) Dispose() {
	iou.mu.Lock()
	defer iou.mu.Unlock()

	if iou.disposed {
		return
	}

	iou.disposed = true
	channel := iou.resource.ToString()
	iou.logService.Trace(fmt.Sprintf("IPC Object URL: Removed channel %s.", channel))

	// Remove IPC handler
	ipcelectronmain.IpcMain.RemoveHandler(channel)
}

// ProtocolMainService extends Disposable and implements IProtocolMainService
type ProtocolMainService struct {
	*basecommon.Disposable

	validRoots         *basecommon.TernarySearchTree[bool]
	validExtensions    map[string]bool
	environmentService environmentcommon.INativeEnvironmentService
	logService         logcommon.ILogService
}

// NewProtocolMainService creates a new ProtocolMainService
func NewProtocolMainService(
	environmentService environmentcommon.INativeEnvironmentService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
	logService logcommon.ILogService,
) *ProtocolMainService {
	disposable := basecommon.NewDisposable()

	pms := &ProtocolMainService{
		Disposable:         disposable,
		validRoots:         basecommon.ForPaths[bool](func() bool { return !basecommon.IsLinux }),
		validExtensions:    make(map[string]bool),
		environmentService: environmentService,
		logService:         logService,
	}

	// Initialize valid extensions
	validExts := []string{".svg", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".mp4", ".otf", ".ttf"}
	for _, ext := range validExts {
		pms.validExtensions[ext] = true
	}

	// Define initial set of roots we allow loading from
	pms.addValidFileRoot(environmentService.AppRoot())
	pms.addValidFileRoot(environmentService.ExtensionsPath())

	defaultProfile := userDataProfilesService.DefaultProfile()
	if defaultProfile != nil && defaultProfile.GlobalStorageHome != nil {
		globalStorageHome := defaultProfile.GlobalStorageHome.With(basecommon.UriComponents{
			Scheme: basecommon.Schemas.File,
		})
		if globalStorageHome != nil {
			pms.addValidFileRoot(globalStorageHome.FSPath())
		}
	}

	workspaceStorageHome := environmentService.WorkspaceStorageHome()
	if workspaceStorageHome != nil {
		workspaceStorageHomeFile := workspaceStorageHome.With(basecommon.UriComponents{
			Scheme: basecommon.Schemas.File,
		})
		if workspaceStorageHomeFile != nil {
			pms.addValidFileRoot(workspaceStorageHomeFile.FSPath())
		}
	}

	// Handle protocols
	pms.handleProtocols()

	return pms
}

// ServiceBrand implements IProtocolMainService
func (pms *ProtocolMainService) ServiceBrand() interface{} {
	return nil
}

// addValidFileRoot adds a path as root to the list of allowed resources
func (pms *ProtocolMainService) addValidFileRoot(root string) {
	// Pass to normalize because we later also do the same for all paths to check against
	normalizedRoot := basecommon.Normalize(root)

	if pms.validRoots.Get(normalizedRoot) == nil {
		pms.validRoots.Set(normalizedRoot, true)
	}
}

// AddValidFileRoot implements IProtocolMainService
func (pms *ProtocolMainService) AddValidFileRoot(root string) basecommon.IDisposable {
	// Pass to normalize because we later also do the same for all paths to check against
	normalizedRoot := basecommon.Normalize(root)

	if pms.validRoots.Get(normalizedRoot) == nil {
		pms.validRoots.Set(normalizedRoot, true)

		return basecommon.ToDisposable(func() {
			pms.validRoots.Delete(normalizedRoot)
		})
	}

	return basecommon.None
}

// handleProtocols sets up protocol handlers
func (pms *ProtocolMainService) handleProtocols() {
	// In a real Electron implementation, this would register protocol handlers
	// For Go implementation, we'll simulate this

	// Register cleanup
	pms.Register(basecommon.ToDisposable(func() {
		// Cleanup protocol handlers
		pms.logService.Trace("ProtocolMainService: Cleaning up protocol handlers")
	}))
}

// handleFileRequest handles file:// protocol requests
func (pms *ProtocolMainService) handleFileRequest(request *ProtocolRequest, callback ProtocolCallback) {
	uri := basecommon.ParseURI(request.URL)

	pms.logService.Error(fmt.Sprintf("Refused to load resource %s from %s: protocol (original URL: %s)",
		uri.FSPath(), basecommon.Schemas.File, request.URL))

	callback(ErrorResult{Error: -3}) // ABORTED
}

// handleResourceRequest handles vscode-file:// protocol requests
func (pms *ProtocolMainService) handleResourceRequest(request *ProtocolRequest, callback ProtocolCallback) {
	path := pms.requestToNormalizedFilePath(request)
	pathBasename := basecommon.Basename(path, "")

	var headers map[string]string

	crossOriginIsolated := pms.environmentService.CrossOriginIsolated()
	if crossOriginIsolated != nil && *crossOriginIsolated {
		if pathBasename == "workbench.html" || pathBasename == "workbench-dev.html" {
			headers = basecommon.CoopAndCoep
		} else {
			headers = basecommon.COI.GetHeadersFromQuery(request.URL)
		}
	}

	// In OSS, evict resources from the memory cache in the renderer process
	if !pms.environmentService.IsBuilt() {
		if headers == nil {
			headers = make(map[string]string)
		}
		for key, value := range basecommon.CacheControlheaders {
			headers[key] = value
		}
	}

	// Document-policy header is needed for collecting JavaScript callstacks
	if pathBasename == "workbench.html" || pathBasename == "workbench-dev.html" {
		if headers == nil {
			headers = make(map[string]string)
		}
		for key, value := range basecommon.DocumentPolicyheaders {
			headers[key] = value
		}
	}

	// First check by validRoots
	if pms.validRoots.FindSubstr(path) != nil {
		callback(FilePathWithHeaders{Path: path, Headers: headers})
		return
	}

	// Then check by validExtensions
	ext := strings.ToLower(basecommon.Extname(path))
	if pms.validExtensions[ext] {
		callback(FilePathWithHeaders{Path: path, Headers: headers})
		return
	}

	// Finally block to load the resource
	pms.logService.Error(fmt.Sprintf("%s: Refused to load resource %s from %s: protocol (original URL: %s)",
		basecommon.Schemas.VscodeFileResource, path, basecommon.Schemas.VscodeFileResource, request.URL))

	callback(ErrorResult{Error: -3}) // ABORTED
}

// requestToNormalizedFilePath converts a request to a normalized file path
func (pms *ProtocolMainService) requestToNormalizedFilePath(request *ProtocolRequest) string {
	// 1.) Use URI.parse() util from us to convert the raw URL into our URI
	requestUri := basecommon.ParseURI(request.URL)

	// 2.) Use FileAccess.uriToFileUri to convert back from a vscode-file: URI to a file: URI
	unnormalizedFileUri := basecommon.FileAccess.UriToFileUri(requestUri)

	// 3.) Strip anything from the URI that could result in relative paths by using normalize
	return basecommon.Normalize(unnormalizedFileUri.FSPath())
}

// CreateIPCObjectUrl implements IProtocolMainService
func (pms *ProtocolMainService) CreateIPCObjectUrl() IIPCObjectUrl[interface{}] {
	// Create unique URI
	resource := basecommon.NewURI(
		"vscode", // used for all our IPC communication (vscode:<channel>)
		"",
		basecommon.GenerateUuid(),
		"",
		"",
	)

	// Create IPC object URL
	ipcObj := &ipcObjectUrl[interface{}]{
		resource:   resource,
		logService: pms.logService,
	}

	// Install IPC handler
	channel := resource.ToString()
	handler := func(event ipcelectronmain.IpcMainInvokeEvent, args ...interface{}) (interface{}, error) {
		ipcObj.mu.Lock()
		defer ipcObj.mu.Unlock()
		return ipcObj.obj, nil
	}

	ipcelectronmain.IpcMain.Handle(channel, handler)

	pms.logService.Trace(fmt.Sprintf("IPC Object URL: Registered new channel %s.", channel))

	return ipcObj
}
