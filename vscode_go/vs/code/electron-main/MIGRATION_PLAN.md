# VS Code Main.go Migration Plan: From Simplified to Typed Interfaces

## Current State Analysis

The current `main.go` implementation uses simplified `interface{}` types for service compatibility. Based on codebase analysis, most required service interfaces already exist and are functional.

## Phase 1: Core Services (Immediate - Low Risk)

### 1.1 Product Service ✅ Ready
- **Interface**: `productcommon.IProductService`
- **Implementation**: `productcommon.ProductService`
- **Constructor**: `productcommon.NewProductService(config *IProductConfiguration)`
- **Risk**: Low - Simple service with no complex dependencies

### 1.2 Log Services ✅ Ready
- **Buffer Logger**: `logcommon.BufferLogger` with `logcommon.NewBufferLogger(level LogLevel)`
- **Log Service**: `logcommon.ILogService` with `logcommon.NewLogService(primary ILogger, ...additional ILogger)`
- **Logger Service**: `logcommon.ILoggerService` with `logelectronmain.NewLoggerMainService()`
- **Risk**: Low - Logging is foundational and well-tested

### 1.3 Environment Service ✅ Ready
- **Interface**: `environmentelectronmain.IEnvironmentMainServiceInterface`
- **Implementation**: `environmentelectronmain.EnvironmentMainService`
- **Constructor**: `environmentelectronmain.NewEnvironmentMainService(args, productService)`
- **Risk**: Low - Core service with clear dependencies

## Phase 2: State and Configuration (Medium Risk)

### 2.1 State Service ✅ Ready
- **Interface**: `statenode.IStateService`
- **Implementation**: `statenode.StateService`
- **Constructor**: `statenode.NewStateService(saveStrategy, environmentService, logService, fileService)`
- **Dependencies**: Environment, Log, File services
- **Risk**: Medium - Depends on file service

### 2.2 Configuration Service ✅ Ready
- **Interface**: `configurationcommon.IConfigurationService`
- **Implementation**: `configurationcommon.ConfigurationService`
- **Constructor**: `configurationcommon.NewConfigurationService(settingsResource, fileService, policyService, logService)`
- **Dependencies**: File, Policy, Log services
- **Risk**: Medium - Depends on file and policy services

## Phase 3: File and URI Services (Higher Risk)

### 3.1 File Service ✅ Ready
- **Interface**: `filescommon.IFileService`
- **Implementation**: `filescommon.FileService`
- **Constructor**: `filescommon.NewFileService(logService ILogService)`
- **Risk**: Medium - Core dependency, but implementation exists

### 3.2 URI Identity Service ✅ Ready
- **Interface**: `uriidentitycommon.IUriIdentityService`
- **Implementation**: `uriidentitycommon.UriIdentityService`
- **Constructor**: `uriidentitycommon.NewUriIdentityService(fileService IFileService)`
- **Risk**: Low - Simple service with clear dependencies

## Phase 4: Complex Services (Highest Risk)

### 4.1 Instantiation Service ✅ Ready
- **Interface**: `instantiationcommon.IInstantiationService`
- **Implementation**: `instantiationcommon.InstantiationService`
- **Constructor**: `instantiationcommon.NewInstantiationService(services, parent)`
- **Risk**: High - Central to dependency injection

### 4.2 Lifecycle Service ✅ Ready
- **Interface**: `lifecycleelectronmain.ILifecycleMainService`
- **Implementation**: `lifecycleelectronmain.LifecycleMainService`
- **Constructor**: `lifecycleelectronmain.NewLifecycleMainService(logService, stateService, environmentMainService)`
- **Dependencies**: Log, State, Environment services
- **Risk**: High - Complex lifecycle management

### 4.3 UserDataProfiles Service ✅ Ready
- **Interface**: `userdataprofileselectronmain.IUserDataProfilesMainService`
- **Implementation**: `userdataprofileselectronmain.UserDataProfilesMainService`
- **Constructor**: `userdataprofileselectronmain.NewUserDataProfilesMainService(stateService, uriIdentityService, environmentService, fileService, logService)`
- **Dependencies**: State, URI Identity, Environment, File, Log services
- **Risk**: High - Many dependencies

## Implementation Order

### Step 1: Investigate Missing Services
1. Find `filescommon.FileService` implementation and constructor
2. Find `uriidentitycommon.IUriIdentityService` implementation
3. Verify all constructor signatures match current usage

### Step 2: Phase 1 Migration (Low Risk)
1. Replace Product Service `interface{}` → `productcommon.IProductService`
2. Replace Log Services `interface{}` → proper typed interfaces
3. Replace Environment Service `interface{}` → `environmentelectronmain.IEnvironmentMainServiceInterface`

### Step 3: Phase 2 Migration (Medium Risk)
1. Add File Service dependency resolution
2. Replace State Service with proper typing
3. Replace Configuration Service with proper typing

### Step 4: Phase 3 & 4 Migration (High Risk)
1. Implement complex service dependencies
2. Replace Instantiation Service
3. Replace Lifecycle Service
4. Replace UserDataProfiles Service

## Risk Mitigation

### Testing Strategy
- Maintain existing test suite throughout migration
- Add integration tests for each phase
- Test service creation and basic functionality
- Verify IPC server functionality remains intact

### Rollback Plan
- Keep simplified implementation as fallback
- Use feature flags to switch between implementations
- Maintain compilation at each step

### Dependency Resolution
- Create missing service implementations if needed
- Use simplified implementations for missing dependencies
- Gradually replace simplified implementations with full ones

## Success Criteria

### Phase 1 Complete
- [ ] Product, Log, Environment services use proper types
- [ ] All tests pass
- [ ] Application starts successfully

### Phase 2 Complete
- [ ] State and Configuration services use proper types
- [ ] Service initialization works correctly
- [ ] Configuration loading functional

### Final Migration Complete
- [ ] All services use proper typed interfaces
- [ ] No `interface{}` types in service creation
- [ ] Full VS Code service architecture functional
- [ ] Integration with Wails v3 working
- [ ] Performance equivalent or better than simplified version

## Next Steps

1. **Immediate**: Investigate missing File and URI Identity services
2. **Week 1**: Implement Phase 1 migration
3. **Week 2**: Implement Phase 2 migration  
4. **Week 3**: Implement Phase 3 & 4 migration
5. **Week 4**: Testing, optimization, and documentation
