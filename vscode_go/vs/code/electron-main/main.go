/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	environmentelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/electron-main"
	environmentnode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/node"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	filesnode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	lifecycleelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/lifecycle/electron-main"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	logelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/electron-main"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofileselectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/electron-main"
)

/**
 * The main VS Code entry point.
 *
 * Note: This class can exist more than once for example when VS Code is already
 * running and a second instance is started from the command line. It will always
 * try to communicate with an existing instance to prevent that 2 VS Code instances
 * are running at the same time.
 */
type CodeMain struct {
	// Context and synchronization
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex
}

// NewCodeMain creates a new CodeMain instance
func NewCodeMain() *CodeMain {
	ctx, cancel := context.WithCancel(context.Background())

	return &CodeMain{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Main is the main entry point
// Equivalent to the main() method in main.ts
func (cm *CodeMain) Main() error {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Fatal error: %v", r)
			os.Exit(1)
		}
	}()

	return cm.startup()
}

// startup performs the main startup sequence
// Equivalent to the private async startup() method in main.ts
func (cm *CodeMain) startup() error {
	// Set the error handler early enough so that we are not getting the
	// default error dialog popping up
	basecommon.SetUnexpectedErrorHandler(func(err interface{}) {
		log.Printf("Unexpected error: %v", err)
	})

	// Create services
	instantiationService, instanceEnvironment, environmentMainService, configurationService, stateMainService, bufferLogger, productService, userDataProfilesMainService, err := cm.createServices()
	if err != nil {
		return fmt.Errorf("failed to create services: %w", err)
	}

	// Init services
	if err := cm.initServices(environmentMainService, userDataProfilesMainService, configurationService, stateMainService, productService); err != nil {
		// Show a dialog for errors that can be resolved by the user
		cm.handleStartupDataDirError(environmentMainService, productService, err)
		return fmt.Errorf("failed to init services: %w", err)
	}

	// Startup
	return cm.startupWithInstantiationService(instantiationService, instanceEnvironment, environmentMainService, configurationService, stateMainService, bufferLogger, productService, userDataProfilesMainService)
}

// createServices creates the core services
// Equivalent to createServices() method in main.ts
func (cm *CodeMain) createServices() (
	instantiationcommon.IInstantiationService,
	basecommon.IProcessEnvironment,
	environmentelectronmain.IEnvironmentMainServiceInterface,
	*configurationcommon.ConfigurationService,
	statenode.IStateService,
	*logcommon.BufferLogger,
	productcommon.IProductService,
	userdataprofileselectronmain.IUserDataProfilesMainService,
	error,
) {
	services := instantiationcommon.NewServiceCollection()
	disposables := basecommon.NewDisposableStore()

	// Product
	productService := productcommon.NewProductService(&productcommon.IProductConfiguration{
		NameShort: "Kawai Agent VS Code",
		Version:   "1.0.0",
	})

	// Environment
	args := cm.resolveArgs()
	environmentMainService := environmentelectronmain.NewEnvironmentMainService(args, productService)
	instanceEnvironment := cm.patchEnvironment(environmentMainService)

	// Logger
	loggerService := logelectronmain.NewLoggerMainService()

	// Log: We need to buffer the spdlog logs until we are sure
	// we are the only instance running, otherwise we'll have concurrent
	// log file access on Windows
	bufferLogger := logcommon.NewBufferLogger(logcommon.LogLevelInfo)
	logService := disposables.Add(logcommon.NewLogService(logcommon.NewNullLogger()))

	// Files
	fileService := filescommon.NewFileService(logService)
	diskFileSystemProvider := filesnode.NewDiskFileSystemProvider(logService)
	fileService.RegisterProvider(basecommon.SchemasFile, diskFileSystemProvider)

	// URI Identity
	uriIdentityService := uriidentitycommon.NewUriIdentityService(fileService)

	// State
	stateService := statenode.NewStateService(
		statenode.SaveStrategyDelayed,
		environmentMainService,
		logService,
		fileService,
	)

	// User Data Profiles
	userDataProfilesMainService := userdataprofileselectronmain.NewUserDataProfilesMainService(
		stateService,
		uriIdentityService,
		environmentMainService,
		fileService,
		logService,
	)

	// Configuration
	configurationService := configurationcommon.NewConfigurationService(
		userDataProfilesMainService.DefaultProfile().SettingsResource(),
		fileService,
		nil, // policyService - simplified for now
		logService,
	)

	instantiationService := instantiationcommon.NewInstantiationService(services, nil)

	return instantiationService, instanceEnvironment, environmentMainService, configurationService, stateService, bufferLogger, productService, userDataProfilesMainService, nil
}

// patchEnvironment patches the environment with instance-specific variables
// Equivalent to patchEnvironment() method in main.ts
func (cm *CodeMain) patchEnvironment(environmentMainService environmentelectronmain.IEnvironmentMainServiceInterface) basecommon.IProcessEnvironment {
	instanceEnvironment := basecommon.IProcessEnvironment{
		"VSCODE_IPC_HOOK": environmentMainService.MainIPCHandle(),
	}

	// Copy specific environment variables
	for _, key := range []string{"VSCODE_NLS_CONFIG", "VSCODE_PORTABLE"} {
		if value := os.Getenv(key); value != "" {
			instanceEnvironment[key] = value
		}
	}

	// Apply to current process environment
	for key, value := range instanceEnvironment {
		os.Setenv(key, value)
	}

	return instanceEnvironment
}

// resolveArgs resolves command line arguments
// Equivalent to resolveArgs() method in main.ts
func (cm *CodeMain) resolveArgs() environmentcommon.NativeParsedArgs {
	args := environmentnode.ParseMainProcessArgv(os.Args)
	return cm.validatePaths(args)
}

// validatePaths validates and normalizes paths in arguments
// Equivalent to validatePaths() method in main.ts
func (cm *CodeMain) validatePaths(args environmentcommon.NativeParsedArgs) environmentcommon.NativeParsedArgs {
	// Track URLs if they're going to be used
	if openURL, exists := args["open-url"]; exists && openURL.(bool) {
		if paths, exists := args["_"]; exists {
			args["_urls"] = paths
			args["_"] = []string{}
		}
	}

	// Normalize paths and watch out for goto line mode
	if _, exists := args["remote"]; !exists {
		if paths, exists := args["_"]; exists {
			gotoMode := false
			if gotoArg, exists := args["goto"]; exists {
				gotoMode = gotoArg.(bool)
			}
			normalizedPaths := cm.doValidatePaths(paths.([]string), gotoMode)
			args["_"] = normalizedPaths
		}
	}

	return args
}

// doValidatePaths validates individual paths
// Equivalent to doValidatePaths() method in main.ts
func (cm *CodeMain) doValidatePaths(args []string, gotoLineMode bool) []string {
	cwd, _ := os.Getwd()
	result := make([]string, 0, len(args))

	for _, arg := range args {
		pathCandidate := strings.TrimSpace(arg)

		if gotoLineMode {
			// Parse line and column information
			parts := strings.Split(pathCandidate, ":")
			if len(parts) > 1 {
				pathCandidate = parts[0]
			}
		}

		if pathCandidate != "" {
			pathCandidate = cm.preparePath(cwd, pathCandidate)
		}

		sanitizedFilePath := basecommon.SanitizeFilePath(pathCandidate, cwd)
		filePathBasename := filepath.Base(sanitizedFilePath)

		if filePathBasename != "" && basecommon.IsValidBasename(filePathBasename) {
			result = append(result, sanitizedFilePath)
		}
	}

	// Remove duplicates
	return basecommon.Distinct(result, func(path string) string {
		if basecommon.IsWindows || basecommon.IsMacintosh {
			return strings.ToLower(path)
		}
		return path
	})
}

// preparePath prepares a path for validation
// Equivalent to preparePath() method in main.ts
func (cm *CodeMain) preparePath(cwd, path string) string {
	// Trim trailing quotes on Windows
	if basecommon.IsWindows {
		path = strings.TrimSuffix(path, "\"")
	}

	// Trim whitespaces
	path = strings.TrimSpace(path)

	if basecommon.IsWindows {
		// Resolve the path against cwd if it is relative
		if !filepath.IsAbs(path) {
			path = filepath.Join(cwd, path)
		}

		// Trim trailing '.' chars on Windows to prevent invalid file names
		path = strings.TrimSuffix(path, ".")
	}

	return path
}

// initServices initializes the services
// Equivalent to initServices() method in main.ts
func (cm *CodeMain) initServices(
	environmentMainService environmentelectronmain.IEnvironmentMainServiceInterface,
	userDataProfilesMainService userdataprofileselectronmain.IUserDataProfilesMainService,
	configurationService *configurationcommon.ConfigurationService,
	stateMainService statenode.IStateService,
	productService productcommon.IProductService,
) error {
	// Initialize state service
	if err := stateMainService.Init(); err != nil {
		return fmt.Errorf("failed to initialize state service: %w", err)
	}

	// Initialize configuration service
	if err := configurationService.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize configuration service: %w", err)
	}

	// Initialize user data profiles after initializing the state
	userDataProfilesMainService.Init()

	return nil
}

// handleStartupDataDirError handles startup data directory errors
// Equivalent to handleStartupDataDirError() method in main.ts
func (cm *CodeMain) handleStartupDataDirError(
	environmentMainService environmentelectronmain.IEnvironmentMainServiceInterface,
	productService productcommon.IProductService,
	err error,
) {
	log.Printf("Startup data directory error: %v", err)
	// In a full implementation, this would show a dialog to the user
	// For now, we just log the error
}

// startupWithInstantiationService performs startup with initialized services
// Equivalent to the instantiationService.invokeFunction call in main.ts
func (cm *CodeMain) startupWithInstantiationService(
	instantiationService instantiationcommon.IInstantiationService,
	instanceEnvironment basecommon.IProcessEnvironment,
	environmentMainService environmentelectronmain.IEnvironmentMainServiceInterface,
	configurationService *configurationcommon.ConfigurationService,
	stateService statenode.IStateService,
	bufferLogger *logcommon.BufferLogger,
	productService productcommon.IProductService,
	userDataProfilesMainService userdataprofileselectronmain.IUserDataProfilesMainService,
) error {
	log.Println("Starting up with services...")

	// Create simplified services for CodeApplication
	logService := logcommon.NewLogService(logcommon.NewNullLogger())
	lifecycleService := lifecycleelectronmain.NewLifecycleMainService()

	// Create CodeApplication (equivalent to new CodeApplication())
	codeApplication, err := NewCodeApplication(
		instantiationService,
		logService,
		logService, // Using same service for logger
		environmentMainService,
		lifecycleService,
		configurationService,
		stateService,
		nil, // fileService - simplified for now
		productService,
		userDataProfilesMainService,
	)
	if err != nil {
		return fmt.Errorf("failed to create code application: %w", err)
	}

	// Start the application (equivalent to app.startup())
	if err := codeApplication.Startup(); err != nil {
		return fmt.Errorf("failed to start code application: %w", err)
	}

	log.Println("VS Code startup completed successfully")
	return nil
}

// Shutdown gracefully shuts down the main process
func (cm *CodeMain) Shutdown() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	log.Println("Shutting down VS Code main process...")
	cm.cancel()
	log.Println("VS Code main process shut down")
	return nil
}

// Context returns the main context
func (cm *CodeMain) Context() context.Context {
	return cm.ctx
}

// Helper functions equivalent to main.ts utilities

// GetAppRoot returns the application root directory
func GetAppRoot() string {
	if execPath, err := os.Executable(); err == nil {
		return filepath.Dir(execPath)
	}
	return "."
}

// GetPlatformInfo returns platform information
func GetPlatformInfo() map[string]interface{} {
	return map[string]interface{}{
		"platform":    runtime.GOOS,
		"arch":        runtime.GOARCH,
		"isWindows":   runtime.GOOS == "windows",
		"isMacintosh": runtime.GOOS == "darwin",
		"isLinux":     runtime.GOOS == "linux",
		"numCPU":      runtime.NumCPU(),
		"goVersion":   runtime.Version(),
	}
}

// ParseArguments parses command line arguments
// Equivalent to parseMainProcessArgv in main.ts
func ParseArguments() map[string]interface{} {
	args := make(map[string]interface{})
	args["args"] = os.Args[1:]
	if cwd, err := os.Getwd(); err == nil {
		args["cwd"] = cwd
	} else {
		args["cwd"] = "."
	}
	return args
}
