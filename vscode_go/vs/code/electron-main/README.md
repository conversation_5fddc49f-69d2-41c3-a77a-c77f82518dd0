# VS Code Electron Main Migration

This directory contains the Go migration of VS Code's main electron process entry point from TypeScript to Go for the Wails v3 implementation.

## Migration Overview

### Source File
- **TypeScript**: `vscode_ts/src/vs/code/electron-main/main.ts`
- **Go**: `vscode_go/vs/code/electron-main/main.go`

### Migration Rules Applied

1. **Directory Structure**: Exact mirroring maintained
   - `vscode_ts/src/vs/code/electron-main/` → `vscode_go/vs/code/electron-main/`

2. **Package Naming**: `package electronmain`

3. **Import Aliases**: Following parent+current directory pattern
   - `basecommon` for `vs/base/common`
   - `basenode` for `vs/base/node`
   - `ipcnode` for `vs/base/parts/ipc/node`

4. **Naming Conventions**: 
   - Preserved original function/method names with Go conventions
   - Exported items start with uppercase (Main, NewCodeMain)
   - Unexported items start with lowercase (startup, createServices)

## Core Components Migrated

### 1. CodeMain Struct
```go
type CodeMain struct {
    ctx    context.Context
    cancel context.CancelFunc
    mutex  sync.RWMutex
}
```
- Equivalent to TypeScript `CodeMain` class
- Manages application lifecycle and context

### 2. Main Entry Point
```go
func (cm *CodeMain) Main() error
```
- Equivalent to TypeScript `main()` method
- Handles panic recovery and calls startup sequence

### 3. Startup Sequence
```go
func (cm *CodeMain) startup() error
```
- Equivalent to TypeScript `private async startup()` method
- Sets error handlers, creates services, initializes services, starts application

### 4. Service Creation
```go
func (cm *CodeMain) createServices() (...)
```
- Equivalent to TypeScript `createServices()` method
- Creates all core services (product, environment, logger, configuration, etc.)
- Simplified implementation using `interface{}` for compatibility

### 5. IPC Server Management
```go
func (cm *CodeMain) claimInstance() (interface{}, error)
```
- Equivalent to TypeScript `claimInstance()` method
- Creates IPC server to ensure single instance
- Uses the migrated IPC functionality

### 6. Command Line Processing
```go
func (cm *CodeMain) resolveArgs() map[string]interface{}
func (cm *CodeMain) validatePaths(args map[string]interface{}) map[string]interface{}
func (cm *CodeMain) doValidatePaths(args []string, gotoLineMode bool) []string
```
- Equivalent to TypeScript argument processing methods
- Handles path validation, normalization, and duplicate removal

## Dependencies Added/Fixed

### 1. IPC Server/Client (`vscode_go/vs/base/parts/ipc/node/ipc.go`)
```go
type NodeIPCServer struct { ... }
type NodeIPCClient[T any] struct { ... }
func Serve(handle string) (*NodeIPCServer, error)
func Connect[T any](handle, clientName string) (*NodeIPCClient[T], error)
```

### 2. ResourceQueue (`vscode_go/vs/base/common/async.go`)
```go
type ResourceQueue struct { ... }
func NewResourceQueue() *ResourceQueue
func (rq *ResourceQueue) QueueFor(uri *URI, taskFactory func() <-chan interface{}) <-chan interface{}
```

## Key Features

### ✅ Implemented
- [x] Main entry point and startup sequence
- [x] Service creation and initialization
- [x] Command line argument parsing
- [x] Path validation and sanitization
- [x] IPC server creation for single instance
- [x] Environment variable patching
- [x] Error handling and recovery
- [x] Graceful shutdown
- [x] Lockfile management
- [x] Cross-platform compatibility

### 🔄 Simplified
- Service implementations use `interface{}` for compatibility
- Simplified service initialization (no complex interface dependencies)
- Basic IPC server implementation (functional but not full-featured)
- Simplified error dialogs (logging instead of UI dialogs)

### 🚧 Future Enhancements
- Full service interface implementations
- Complete IPC protocol implementation
- Native dialog integration for errors
- CodeApplication integration
- Full VS Code service ecosystem

## Testing

The migration includes comprehensive tests covering:
- CodeMain creation and lifecycle
- Command line argument processing
- Path validation and sanitization
- Service creation
- IPC handle generation
- Duplicate removal utilities

Run tests with:
```bash
cd vscode_go/vs/code/electron-main
go test -v
```

## Usage

```go
package main

import (
    electronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/code/electron-main"
)

func main() {
    codeMain := electronmain.NewCodeMain()
    if err := codeMain.Main(); err != nil {
        log.Fatalf("Failed to start VS Code: %v", err)
    }
}
```

## Architecture Notes

### Wails v3 Integration
- Replaces Electron IPC with Wails service bindings
- Maintains VS Code's service architecture
- Provides foundation for frontend-backend communication

### Error Handling
- Comprehensive error handling with proper Go error wrapping
- Panic recovery at main entry point
- Graceful degradation for service failures

### Cross-Platform Support
- Platform-specific IPC handle generation
- Windows/Unix socket handling
- Path normalization for different platforms

This migration provides a solid foundation for running VS Code's main process logic in a Wails v3 environment while maintaining compatibility with the original TypeScript architecture.
