/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"testing"
	"time"
)

func TestCodeMainCreation(t *testing.T) {
	codeMain := NewCodeMain()
	if codeMain == nil {
		t.Fatal("NewCodeMain() returned nil")
	}
	
	if codeMain.ctx == nil {
		t.Error("CodeMain context is nil")
	}
	
	if codeMain.cancel == nil {
		t.Error("CodeMain cancel function is nil")
	}
}

func TestCodeMainContext(t *testing.T) {
	codeMain := NewCodeMain()
	ctx := codeMain.Context()
	
	if ctx == nil {
		t.Fatal("Context() returned nil")
	}
	
	// Test context cancellation
	codeMain.cancel()
	
	select {
	case <-ctx.Done():
		// Expected behavior
	case <-time.After(100 * time.Millisecond):
		t.Error("Context was not cancelled")
	}
}

func TestResolveArgs(t *testing.T) {
	codeMain := NewCodeMain()
	args := codeMain.resolveArgs()
	
	if args == nil {
		t.Fatal("resolveArgs() returned nil")
	}
	
	// Check that basic fields are present
	if _, exists := args["_"]; !exists {
		t.Error("resolveArgs() missing '_' field")
	}
	
	if _, exists := args["goto"]; !exists {
		t.Error("resolveArgs() missing 'goto' field")
	}
}

func TestValidatePaths(t *testing.T) {
	codeMain := NewCodeMain()
	
	testArgs := map[string]interface{}{
		"_":        []string{"test.txt", "another.txt"},
		"goto":     false,
		"remote":   false,
		"open-url": false,
	}
	
	result := codeMain.validatePaths(testArgs)
	
	if result == nil {
		t.Fatal("validatePaths() returned nil")
	}
	
	// Should have the same structure
	if _, exists := result["_"]; !exists {
		t.Error("validatePaths() missing '_' field")
	}
}

func TestDoValidatePaths(t *testing.T) {
	codeMain := NewCodeMain()
	
	testPaths := []string{"test.txt", "another.txt", "test.txt"} // Include duplicate
	result := codeMain.doValidatePaths(testPaths, false)
	
	if len(result) == 0 {
		t.Error("doValidatePaths() returned empty slice")
	}
	
	// Should remove duplicates
	if len(result) >= len(testPaths) {
		t.Error("doValidatePaths() did not remove duplicates")
	}
}

func TestIsValidBasename(t *testing.T) {
	codeMain := NewCodeMain()
	
	// Test valid basenames
	validNames := []string{"test.txt", "file", "my-file.go"}
	for _, name := range validNames {
		if !codeMain.isValidBasename(name) {
			t.Errorf("isValidBasename() incorrectly rejected valid name: %s", name)
		}
	}
	
	// Test invalid basenames
	invalidNames := []string{"", ".", "..", "file<test", "file>test", "file:test"}
	for _, name := range invalidNames {
		if codeMain.isValidBasename(name) {
			t.Errorf("isValidBasename() incorrectly accepted invalid name: %s", name)
		}
	}
}

func TestRemoveDuplicates(t *testing.T) {
	codeMain := NewCodeMain()
	
	testSlice := []string{"a", "b", "a", "c", "b", "d"}
	result := codeMain.removeDuplicates(testSlice)
	
	if len(result) != 4 {
		t.Errorf("removeDuplicates() expected 4 unique items, got %d", len(result))
	}
	
	// Check that all unique items are present
	expected := map[string]bool{"a": true, "b": true, "c": true, "d": true}
	for _, item := range result {
		if !expected[item] {
			t.Errorf("removeDuplicates() returned unexpected item: %s", item)
		}
	}
}

func TestGetMainIPCHandle(t *testing.T) {
	codeMain := NewCodeMain()
	handle := codeMain.getMainIPCHandle()
	
	if handle == "" {
		t.Error("getMainIPCHandle() returned empty string")
	}
	
	// Should return different handles based on platform
	// This is a basic test to ensure it returns something reasonable
	if len(handle) < 5 {
		t.Error("getMainIPCHandle() returned suspiciously short handle")
	}
}

func TestCreateServices(t *testing.T) {
	codeMain := NewCodeMain()
	
	instantiationService, instanceEnvironment, environmentMainService, configurationService, stateMainService, bufferLogger, productService, userDataProfilesMainService, err := codeMain.createServices()
	
	if err != nil {
		t.Fatalf("createServices() returned error: %v", err)
	}
	
	// Check that all services are not nil
	if instantiationService == nil {
		t.Error("createServices() returned nil instantiationService")
	}
	
	if instanceEnvironment == nil {
		t.Error("createServices() returned nil instanceEnvironment")
	}
	
	if environmentMainService == nil {
		t.Error("createServices() returned nil environmentMainService")
	}
	
	if configurationService == nil {
		t.Error("createServices() returned nil configurationService")
	}
	
	if stateMainService == nil {
		t.Error("createServices() returned nil stateMainService")
	}
	
	if bufferLogger == nil {
		t.Error("createServices() returned nil bufferLogger")
	}
	
	if productService == nil {
		t.Error("createServices() returned nil productService")
	}
	
	if userDataProfilesMainService == nil {
		t.Error("createServices() returned nil userDataProfilesMainService")
	}
}

func TestInitServices(t *testing.T) {
	codeMain := NewCodeMain()
	
	// Create mock services
	environmentMainService := map[string]interface{}{"test": true}
	userDataProfilesMainService := map[string]interface{}{"test": true}
	configurationService := map[string]interface{}{"test": true}
	stateMainService := map[string]interface{}{"test": true}
	productService := map[string]interface{}{"test": true}
	
	err := codeMain.initServices(environmentMainService, userDataProfilesMainService, configurationService, stateMainService, productService)
	
	if err != nil {
		t.Errorf("initServices() returned error: %v", err)
	}
}
