/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// Package main provides an example of how to use the migrated VS Code electron main process
package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	electronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/code/electron-main"
)

func main() {
	log.Println("Starting VS Code Main Process (Go Implementation)")

	// Create the main VS Code instance
	codeMain := electronmain.NewCodeMain()
	if codeMain == nil {
		log.Fatal("Failed to create CodeMain instance")
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the shutdown handler in a goroutine
	go func() {
		<-sigChan
		log.Println("Received shutdown signal, shutting down gracefully...")
		if err := codeMain.Shutdown(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}
		os.Exit(0)
	}()

	// Start the main VS Code process
	log.Println("Calling CodeMain.Main()...")
	if err := codeMain.Main(); err != nil {
		log.Fatalf("VS Code main process failed: %v", err)
	}

	log.Println("VS Code main process completed successfully")
}
